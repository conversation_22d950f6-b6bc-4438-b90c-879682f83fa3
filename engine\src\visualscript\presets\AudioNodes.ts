/**
 * 音频处理节点
 * 提供音频加载、播放、控制、效果处理等功能
 */

import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 音频加载节点
 */
export class AudioLoadNode extends FlowNode {
  private audioBuffer: AudioBuffer | null = null;
  private audioContext: AudioContext | null = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['loaded', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '音频加载';
    }
    if (!this.metadata.description) {
      this.metadata.description = '加载音频文件';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '音频文件URL',
      defaultValue: ''
    });

    this.addInput({
      name: 'preload',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '预加载',
      defaultValue: true
    });

    this.addInput({
      name: 'crossOrigin',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '跨域设置',
      defaultValue: 'anonymous'
    });

    // 输出
    this.addOutput({
      name: 'audioBuffer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '音频缓冲区'
    });

    this.addOutput({
      name: 'audioInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '音频信息'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '音频时长（秒）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const url = inputs.url;
      const preload = inputs.preload !== false;
      const crossOrigin = inputs.crossOrigin || 'anonymous';

      if (!url) {
        throw new Error('音频文件URL不能为空');
      }

      // 创建音频上下文
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      }

      // 加载音频文件
      const response = await fetch(url, {
        mode: crossOrigin === 'anonymous' ? 'cors' : 'no-cors'
      });

      if (!response.ok) {
        throw new Error(`加载音频文件失败: ${response.status}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      this.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

      const audioInfo = {
        url,
        duration: this.audioBuffer.duration,
        sampleRate: this.audioBuffer.sampleRate,
        numberOfChannels: this.audioBuffer.numberOfChannels,
        length: this.audioBuffer.length,
        loadedAt: new Date().toISOString()
      };

      this.setOutputValue('audioBuffer', this.audioBuffer);
      this.setOutputValue('audioInfo', audioInfo);
      this.setOutputValue('duration', this.audioBuffer.duration);
      
      return 'loaded';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  /**
   * 获取音频上下文
   */
  public getAudioContext(): AudioContext | null {
    return this.audioContext;
  }

  /**
   * 获取音频缓冲区
   */
  public getAudioBuffer(): AudioBuffer | null {
    return this.audioBuffer;
  }
}

/**
 * 音频播放节点
 */
export class AudioPlayNode extends FlowNode {
  private audioSource: AudioBufferSourceNode | null = null;
  private audioContext: AudioContext | null = null;
  private gainNode: GainNode | null = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['playing', 'ended', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '音频播放';
    }
    if (!this.metadata.description) {
      this.metadata.description = '播放音频';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'audioBuffer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频缓冲区'
    });

    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频上下文'
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '循环播放',
      defaultValue: false
    });

    this.addInput({
      name: 'volume',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '音量（0-1）',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'startTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '开始时间（秒）',
      defaultValue: 0
    });

    this.addInput({
      name: 'playbackRate',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '播放速率',
      defaultValue: 1.0
    });

    // 输出
    this.addOutput({
      name: 'audioSource',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '音频源节点'
    });

    this.addOutput({
      name: 'gainNode',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '增益节点'
    });

    this.addOutput({
      name: 'playbackInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '播放信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const audioBuffer = inputs.audioBuffer;
      const audioContext = inputs.audioContext;
      const loop = inputs.loop || false;
      const volume = Math.max(0, Math.min(1, inputs.volume || 1.0));
      const startTime = inputs.startTime || 0;
      const playbackRate = inputs.playbackRate || 1.0;

      if (!audioBuffer) {
        throw new Error('音频缓冲区不能为空');
      }

      // 使用提供的音频上下文或创建新的
      this.audioContext = audioContext || new (window.AudioContext || (window as any).webkitAudioContext)();

      // 停止之前的播放
      if (this.audioSource) {
        this.audioSource.stop();
      }

      // 创建音频源节点
      this.audioSource = this.audioContext.createBufferSource();
      this.audioSource.buffer = audioBuffer;
      this.audioSource.loop = loop;
      this.audioSource.playbackRate.value = playbackRate;

      // 创建增益节点控制音量
      this.gainNode = this.audioContext.createGain();
      this.gainNode.gain.value = volume;

      // 连接音频节点
      this.audioSource.connect(this.gainNode);
      this.gainNode.connect(this.audioContext.destination);

      // 设置播放结束回调
      this.audioSource.onended = () => {
        this.triggerFlow('ended');
      };

      // 开始播放
      this.audioSource.start(0, startTime);

      const playbackInfo = {
        loop,
        volume,
        startTime,
        playbackRate,
        duration: audioBuffer.duration,
        startedAt: new Date().toISOString()
      };

      this.setOutputValue('audioSource', this.audioSource);
      this.setOutputValue('gainNode', this.gainNode);
      this.setOutputValue('playbackInfo', playbackInfo);
      
      return 'playing';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  /**
   * 停止播放
   */
  public stop(): void {
    if (this.audioSource) {
      this.audioSource.stop();
      this.audioSource = null;
    }
  }

  /**
   * 设置音量
   */
  public setVolume(volume: number): void {
    if (this.gainNode) {
      this.gainNode.gain.value = Math.max(0, Math.min(1, volume));
    }
  }

  /**
   * 获取当前播放状态
   */
  public isPlaying(): boolean {
    return this.audioSource !== null;
  }
}

/**
 * 音频暂停节点
 */
export class AudioPauseNode extends FlowNode {
  private pausedAt: number = 0;
  private startOffset: number = 0;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['paused', 'resumed', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '音频暂停';
    }
    if (!this.metadata.description) {
      this.metadata.description = '暂停/恢复音频播放';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'audioSource',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频源节点'
    });

    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频上下文'
    });

    this.addInput({
      name: 'action',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作（pause/resume）',
      defaultValue: 'pause'
    });

    // 输出
    this.addOutput({
      name: 'pauseInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '暂停信息'
    });

    this.addOutput({
      name: 'currentTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '当前播放时间'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const audioSource = inputs.audioSource;
      const audioContext = inputs.audioContext;
      const action = inputs.action || 'pause';

      if (!audioContext) {
        throw new Error('音频上下文不能为空');
      }

      switch (action.toLowerCase()) {
        case 'pause':
          return this.pauseAudio(audioContext);

        case 'resume':
          return this.resumeAudio(audioContext);

        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private pauseAudio(audioContext: AudioContext): string {
    if (audioContext.state === 'running') {
      this.pausedAt = audioContext.currentTime;
      audioContext.suspend();

      const pauseInfo = {
        action: 'pause',
        pausedAt: this.pausedAt,
        timestamp: new Date().toISOString()
      };

      this.setOutputValue('pauseInfo', pauseInfo);
      this.setOutputValue('currentTime', this.pausedAt);

      return 'paused';
    }

    return 'paused'; // 已经暂停
  }

  private resumeAudio(audioContext: AudioContext): string {
    if (audioContext.state === 'suspended') {
      audioContext.resume();

      const pauseInfo = {
        action: 'resume',
        resumedAt: audioContext.currentTime,
        pauseDuration: audioContext.currentTime - this.pausedAt,
        timestamp: new Date().toISOString()
      };

      this.setOutputValue('pauseInfo', pauseInfo);
      this.setOutputValue('currentTime', audioContext.currentTime);

      return 'resumed';
    }

    return 'resumed'; // 已经在播放
  }
}

/**
 * 音频停止节点
 */
export class AudioStopNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['stopped', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '音频停止';
    }
    if (!this.metadata.description) {
      this.metadata.description = '停止音频播放';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'audioSource',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频源节点'
    });

    this.addInput({
      name: 'fadeOut',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '淡出停止',
      defaultValue: false
    });

    this.addInput({
      name: 'fadeTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '淡出时间（秒）',
      defaultValue: 1.0
    });

    // 输出
    this.addOutput({
      name: 'stopInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '停止信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const audioSource = inputs.audioSource;
      const fadeOut = inputs.fadeOut || false;
      const fadeTime = inputs.fadeTime || 1.0;

      if (!audioSource) {
        throw new Error('音频源节点不能为空');
      }

      if (fadeOut) {
        // 淡出停止
        await this.fadeOutAndStop(audioSource, fadeTime);
      } else {
        // 立即停止
        audioSource.stop();
      }

      const stopInfo = {
        fadeOut,
        fadeTime: fadeOut ? fadeTime : 0,
        stoppedAt: new Date().toISOString()
      };

      this.setOutputValue('stopInfo', stopInfo);

      return 'stopped';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async fadeOutAndStop(audioSource: AudioBufferSourceNode, fadeTime: number): Promise<void> {
    return new Promise((resolve) => {
      // 获取增益节点（假设音频源连接到增益节点）
      const gainNode = audioSource.context.createGain();

      // 设置淡出
      const currentTime = audioSource.context.currentTime;
      gainNode.gain.setValueAtTime(1, currentTime);
      gainNode.gain.linearRampToValueAtTime(0, currentTime + fadeTime);

      // 在淡出完成后停止
      setTimeout(() => {
        audioSource.stop();
        resolve();
      }, fadeTime * 1000);
    });
  }
}

/**
 * 音频音量控制节点
 */
export class AudioVolumeNode extends FlowNode {
  private gainNode: GainNode | null = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['volumeSet', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '音频音量';
    }
    if (!this.metadata.description) {
      this.metadata.description = '控制音频音量';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频上下文'
    });

    this.addInput({
      name: 'inputNode',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '输入音频节点'
    });

    this.addInput({
      name: 'volume',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '音量（0-1）',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'fadeTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '淡入淡出时间（秒）',
      defaultValue: 0
    });

    this.addInput({
      name: 'mute',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '静音',
      defaultValue: false
    });

    // 输出
    this.addOutput({
      name: 'gainNode',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '增益节点'
    });

    this.addOutput({
      name: 'currentVolume',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '当前音量'
    });

    this.addOutput({
      name: 'volumeInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '音量信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const audioContext = inputs.audioContext;
      const inputNode = inputs.inputNode;
      const volume = Math.max(0, Math.min(1, inputs.volume || 1.0));
      const fadeTime = inputs.fadeTime || 0;
      const mute = inputs.mute || false;

      if (!audioContext) {
        throw new Error('音频上下文不能为空');
      }

      // 创建增益节点
      if (!this.gainNode) {
        this.gainNode = audioContext.createGain();
      }

      // 连接输入节点
      if (inputNode) {
        inputNode.connect(this.gainNode);
      }

      // 设置音量
      const targetVolume = mute ? 0 : volume;
      const currentTime = audioContext.currentTime;

      if (fadeTime > 0) {
        // 淡入淡出
        this.gainNode.gain.setValueAtTime(this.gainNode.gain.value, currentTime);
        this.gainNode.gain.linearRampToValueAtTime(targetVolume, currentTime + fadeTime);
      } else {
        // 立即设置
        this.gainNode.gain.setValueAtTime(targetVolume, currentTime);
      }

      const volumeInfo = {
        volume,
        targetVolume,
        fadeTime,
        mute,
        setAt: new Date().toISOString()
      };

      this.setOutputValue('gainNode', this.gainNode);
      this.setOutputValue('currentVolume', targetVolume);
      this.setOutputValue('volumeInfo', volumeInfo);

      return 'volumeSet';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  /**
   * 获取增益节点
   */
  public getGainNode(): GainNode | null {
    return this.gainNode;
  }

  /**
   * 设置音量
   */
  public setVolume(volume: number, fadeTime: number = 0): void {
    if (this.gainNode) {
      const targetVolume = Math.max(0, Math.min(1, volume));
      const currentTime = this.gainNode.context.currentTime;

      if (fadeTime > 0) {
        this.gainNode.gain.setValueAtTime(this.gainNode.gain.value, currentTime);
        this.gainNode.gain.linearRampToValueAtTime(targetVolume, currentTime + fadeTime);
      } else {
        this.gainNode.gain.setValueAtTime(targetVolume, currentTime);
      }
    }
  }
}

/**
 * 3D音频节点
 */
export class Audio3DNode extends FlowNode {
  private pannerNode: PannerNode | null = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['positioned', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '3D音频';
    }
    if (!this.metadata.description) {
      this.metadata.description = '3D空间音频定位';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频上下文'
    });

    this.addInput({
      name: 'inputNode',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '输入音频节点'
    });

    this.addInput({
      name: 'positionX',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'X坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'positionY',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Y坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'positionZ',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Z坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'orientationX',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '方向X',
      defaultValue: 1
    });

    this.addInput({
      name: 'orientationY',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '方向Y',
      defaultValue: 0
    });

    this.addInput({
      name: 'orientationZ',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '方向Z',
      defaultValue: 0
    });

    this.addInput({
      name: 'maxDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大距离',
      defaultValue: 10000
    });

    this.addInput({
      name: 'refDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '参考距离',
      defaultValue: 1
    });

    this.addInput({
      name: 'rolloffFactor',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '衰减因子',
      defaultValue: 1
    });

    // 输出
    this.addOutput({
      name: 'pannerNode',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '3D音频节点'
    });

    this.addOutput({
      name: 'spatialInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '空间音频信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const audioContext = inputs.audioContext;
      const inputNode = inputs.inputNode;
      const positionX = inputs.positionX || 0;
      const positionY = inputs.positionY || 0;
      const positionZ = inputs.positionZ || 0;
      const orientationX = inputs.orientationX || 1;
      const orientationY = inputs.orientationY || 0;
      const orientationZ = inputs.orientationZ || 0;
      const maxDistance = inputs.maxDistance || 10000;
      const refDistance = inputs.refDistance || 1;
      const rolloffFactor = inputs.rolloffFactor || 1;

      if (!audioContext) {
        throw new Error('音频上下文不能为空');
      }

      // 创建3D音频节点
      if (!this.pannerNode) {
        this.pannerNode = audioContext.createPanner();
      }

      // 设置3D音频属性
      this.pannerNode.panningModel = 'HRTF';
      this.pannerNode.distanceModel = 'inverse';
      this.pannerNode.maxDistance = maxDistance;
      this.pannerNode.refDistance = refDistance;
      this.pannerNode.rolloffFactor = rolloffFactor;

      // 设置位置
      this.pannerNode.positionX.setValueAtTime(positionX, audioContext.currentTime);
      this.pannerNode.positionY.setValueAtTime(positionY, audioContext.currentTime);
      this.pannerNode.positionZ.setValueAtTime(positionZ, audioContext.currentTime);

      // 设置方向
      this.pannerNode.orientationX.setValueAtTime(orientationX, audioContext.currentTime);
      this.pannerNode.orientationY.setValueAtTime(orientationY, audioContext.currentTime);
      this.pannerNode.orientationZ.setValueAtTime(orientationZ, audioContext.currentTime);

      // 连接输入节点
      if (inputNode) {
        inputNode.connect(this.pannerNode);
      }

      const spatialInfo = {
        position: { x: positionX, y: positionY, z: positionZ },
        orientation: { x: orientationX, y: orientationY, z: orientationZ },
        maxDistance,
        refDistance,
        rolloffFactor,
        panningModel: 'HRTF',
        distanceModel: 'inverse',
        setAt: new Date().toISOString()
      };

      this.setOutputValue('pannerNode', this.pannerNode);
      this.setOutputValue('spatialInfo', spatialInfo);

      return 'positioned';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  /**
   * 设置位置
   */
  public setPosition(x: number, y: number, z: number): void {
    if (this.pannerNode) {
      const currentTime = this.pannerNode.context.currentTime;
      this.pannerNode.positionX.setValueAtTime(x, currentTime);
      this.pannerNode.positionY.setValueAtTime(y, currentTime);
      this.pannerNode.positionZ.setValueAtTime(z, currentTime);
    }
  }

  /**
   * 设置方向
   */
  public setOrientation(x: number, y: number, z: number): void {
    if (this.pannerNode) {
      const currentTime = this.pannerNode.context.currentTime;
      this.pannerNode.orientationX.setValueAtTime(x, currentTime);
      this.pannerNode.orientationY.setValueAtTime(y, currentTime);
      this.pannerNode.orientationZ.setValueAtTime(z, currentTime);
    }
  }
}

/**
 * 音频混合器节点
 */
export class AudioMixerNode extends FlowNode {
  private mixerNode: GainNode | null = null;
  private inputNodes: GainNode[] = [];

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['mixed', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '音频混合器';
    }
    if (!this.metadata.description) {
      this.metadata.description = '音频混合器';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'audioContext',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频上下文'
    });

    this.addInput({
      name: 'inputSources',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '输入音频源数组',
      defaultValue: []
    });

    this.addInput({
      name: 'inputVolumes',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '输入音量数组',
      defaultValue: []
    });

    this.addInput({
      name: 'masterVolume',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '主音量',
      defaultValue: 1.0
    });

    // 输出
    this.addOutput({
      name: 'mixerOutput',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '混合器输出节点'
    });

    this.addOutput({
      name: 'mixerInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '混合器信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const audioContext = inputs.audioContext;
      const inputSources = inputs.inputSources || [];
      const inputVolumes = inputs.inputVolumes || [];
      const masterVolume = Math.max(0, Math.min(1, inputs.masterVolume || 1.0));

      if (!audioContext) {
        throw new Error('音频上下文不能为空');
      }

      // 创建主混合器节点
      if (!this.mixerNode) {
        this.mixerNode = audioContext.createGain();
      }

      this.mixerNode.gain.value = masterVolume;

      // 清除之前的输入节点
      this.inputNodes.forEach(node => node.disconnect());
      this.inputNodes = [];

      // 为每个输入源创建增益节点
      inputSources.forEach((source: AudioNode, index: number) => {
        const inputGain = audioContext.createGain();
        const volume = inputVolumes[index] || 1.0;
        inputGain.gain.value = Math.max(0, Math.min(1, volume));

        // 连接：输入源 -> 输入增益 -> 主混合器
        source.connect(inputGain);
        inputGain.connect(this.mixerNode!);

        this.inputNodes.push(inputGain);
      });

      const mixerInfo = {
        inputCount: inputSources.length,
        masterVolume,
        inputVolumes: inputVolumes.slice(0, inputSources.length),
        mixedAt: new Date().toISOString()
      };

      this.setOutputValue('mixerOutput', this.mixerNode);
      this.setOutputValue('mixerInfo', mixerInfo);

      return 'mixed';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  /**
   * 设置输入音量
   */
  public setInputVolume(index: number, volume: number): void {
    if (this.inputNodes[index]) {
      this.inputNodes[index].gain.value = Math.max(0, Math.min(1, volume));
    }
  }

  /**
   * 设置主音量
   */
  public setMasterVolume(volume: number): void {
    if (this.mixerNode) {
      this.mixerNode.gain.value = Math.max(0, Math.min(1, volume));
    }
  }
}

/**
 * 注册音频处理节点
 */
export function registerAudioNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'audio/load',
    name: '音频加载',
    category: '音频节点',
    description: '加载音频文件',
    factory: (options: FlowNodeOptions) => new AudioLoadNode(options)
  });

  registry.registerNodeType({
    type: 'audio/play',
    name: '音频播放',
    category: '音频节点',
    description: '播放音频',
    factory: (options: FlowNodeOptions) => new AudioPlayNode(options)
  });

  registry.registerNodeType({
    type: 'audio/pause',
    name: '音频暂停',
    category: '音频节点',
    description: '暂停/恢复音频播放',
    factory: (options: FlowNodeOptions) => new AudioPauseNode(options)
  });

  registry.registerNodeType({
    type: 'audio/stop',
    name: '音频停止',
    category: '音频节点',
    description: '停止音频播放',
    factory: (options: FlowNodeOptions) => new AudioStopNode(options)
  });

  registry.registerNodeType({
    type: 'audio/volume',
    name: '音频音量',
    category: '音频节点',
    description: '控制音频音量',
    factory: (options: FlowNodeOptions) => new AudioVolumeNode(options)
  });

  registry.registerNodeType({
    type: 'audio/3d',
    name: '3D音频',
    category: '音频节点',
    description: '3D空间音频定位',
    factory: (options: FlowNodeOptions) => new Audio3DNode(options)
  });

  registry.registerNodeType({
    type: 'audio/mixer',
    name: '音频混合器',
    category: '音频节点',
    description: '音频混合器',
    factory: (options: FlowNodeOptions) => new AudioMixerNode(options)
  });
}
