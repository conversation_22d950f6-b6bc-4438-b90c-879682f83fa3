/**
 * 网络通信节点
 * 提供WebSocket、TCP/UDP套接字、网络状态检测等功能
 */

import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * WebSocket连接节点
 */
export class WebSocketConnectNode extends FlowNode {
  private websocket: WebSocket | null = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['connected', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'WebSocket连接';
    }
    if (!this.metadata.description) {
      this.metadata.description = '建立WebSocket连接';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'WebSocket URL',
      defaultValue: 'ws://localhost:8080'
    });

    this.addInput({
      name: 'protocols',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '协议列表',
      defaultValue: []
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: {}
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '连接超时（毫秒）',
      defaultValue: 5000
    });

    // 输出
    this.addOutput({
      name: 'websocket',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'WebSocket对象'
    });

    this.addOutput({
      name: 'connectionInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '连接信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const url = inputs.url;
      const protocols = inputs.protocols || [];
      const headers = inputs.headers || {};
      const timeout = inputs.timeout || 5000;

      if (!url) {
        throw new Error('WebSocket URL不能为空');
      }

      // 创建WebSocket连接
      this.websocket = new WebSocket(url, protocols);

      // 设置连接超时
      const timeoutId = setTimeout(() => {
        if (this.websocket && this.websocket.readyState === WebSocket.CONNECTING) {
          this.websocket.close();
          this.setOutputValue('error', '连接超时');
        }
      }, timeout);

      return new Promise((resolve) => {
        if (!this.websocket) {
          resolve('failed');
          return;
        }

        this.websocket.onopen = () => {
          clearTimeout(timeoutId);
          
          const connectionInfo = {
            url,
            protocols: this.websocket?.protocol || '',
            readyState: this.websocket?.readyState,
            connectedAt: new Date().toISOString()
          };

          this.setOutputValue('websocket', this.websocket);
          this.setOutputValue('connectionInfo', connectionInfo);
          resolve('connected');
        };

        this.websocket.onerror = (error) => {
          clearTimeout(timeoutId);
          this.setOutputValue('error', '连接失败');
          resolve('failed');
        };

        this.websocket.onclose = () => {
          clearTimeout(timeoutId);
          this.websocket = null;
        };
      });
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  /**
   * 关闭WebSocket连接
   */
  public closeConnection(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }

  /**
   * 获取连接状态
   */
  public getConnectionState(): number {
    return this.websocket?.readyState || WebSocket.CLOSED;
  }
}

/**
 * WebSocket发送节点
 */
export class WebSocketSendNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['sent', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'WebSocket发送';
    }
    if (!this.metadata.description) {
      this.metadata.description = '发送WebSocket消息';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'websocket',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebSocket对象'
    });

    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '消息内容',
      defaultValue: ''
    });

    this.addInput({
      name: 'messageType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '消息类型（text/binary/json）',
      defaultValue: 'text'
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据对象（用于JSON类型）'
    });

    // 输出
    this.addOutput({
      name: 'sentMessage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '已发送的消息'
    });

    this.addOutput({
      name: 'messageSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '消息大小（字节）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const websocket = inputs.websocket;
      const message = inputs.message;
      const messageType = inputs.messageType || 'text';
      const data = inputs.data;

      if (!websocket) {
        throw new Error('WebSocket对象不能为空');
      }

      if (websocket.readyState !== WebSocket.OPEN) {
        throw new Error('WebSocket连接未打开');
      }

      let messageToSend: string;

      switch (messageType.toLowerCase()) {
        case 'text':
          messageToSend = message || '';
          break;

        case 'json':
          if (data) {
            messageToSend = JSON.stringify(data);
          } else if (message) {
            messageToSend = message;
          } else {
            throw new Error('JSON消息需要数据对象或消息内容');
          }
          break;

        case 'binary':
          // 对于二进制数据，这里简化处理
          messageToSend = message || '';
          break;

        default:
          throw new Error(`不支持的消息类型: ${messageType}`);
      }

      // 发送消息
      websocket.send(messageToSend);

      const messageSize = new Blob([messageToSend]).size;

      this.setOutputValue('sentMessage', messageToSend);
      this.setOutputValue('messageSize', messageSize);
      
      return 'sent';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * WebSocket接收节点
 */
export class WebSocketReceiveNode extends FlowNode {
  private messageHandler: ((event: MessageEvent) => void) | null = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['received', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'WebSocket接收';
    }
    if (!this.metadata.description) {
      this.metadata.description = '接收WebSocket消息';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'websocket',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebSocket对象'
    });

    this.addInput({
      name: 'autoParseJSON',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '自动解析JSON',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '接收到的消息'
    });

    this.addOutput({
      name: 'parsedData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '解析后的数据'
    });

    this.addOutput({
      name: 'messageInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '消息信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const websocket = inputs.websocket;
      const autoParseJSON = inputs.autoParseJSON !== false;

      if (!websocket) {
        throw new Error('WebSocket对象不能为空');
      }

      if (websocket.readyState !== WebSocket.OPEN) {
        throw new Error('WebSocket连接未打开');
      }

      // 移除之前的消息处理器
      if (this.messageHandler) {
        websocket.removeEventListener('message', this.messageHandler);
      }

      // 设置新的消息处理器
      this.messageHandler = (event: MessageEvent) => {
        try {
          const message = event.data;
          let parsedData = null;

          // 尝试解析JSON
          if (autoParseJSON && typeof message === 'string') {
            try {
              parsedData = JSON.parse(message);
            } catch {
              // 不是有效的JSON，保持原始数据
            }
          }

          const messageInfo = {
            type: typeof message,
            size: new Blob([message]).size,
            timestamp: new Date().toISOString(),
            origin: event.origin || 'unknown'
          };

          this.setOutputValue('message', message);
          this.setOutputValue('parsedData', parsedData);
          this.setOutputValue('messageInfo', messageInfo);
          
          this.triggerFlow('received');
        } catch (error) {
          this.setOutputValue('error', error.message);
          this.triggerFlow('failed');
        }
      };

      websocket.addEventListener('message', this.messageHandler);
      
      return null; // 不立即触发输出，等待消息到达
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  /**
   * 停止监听消息
   */
  public stopListening(websocket: WebSocket): void {
    if (this.messageHandler && websocket) {
      websocket.removeEventListener('message', this.messageHandler);
      this.messageHandler = null;
    }
  }
}

/**
 * WebSocket关闭节点
 */
export class WebSocketCloseNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['closed', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'WebSocket关闭';
    }
    if (!this.metadata.description) {
      this.metadata.description = '关闭WebSocket连接';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'websocket',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebSocket对象'
    });

    this.addInput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '关闭代码',
      defaultValue: 1000
    });

    this.addInput({
      name: 'reason',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '关闭原因',
      defaultValue: 'Normal closure'
    });

    // 输出
    this.addOutput({
      name: 'closeInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '关闭信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const websocket = inputs.websocket;
      const code = inputs.code || 1000;
      const reason = inputs.reason || 'Normal closure';

      if (!websocket) {
        throw new Error('WebSocket对象不能为空');
      }

      if (websocket.readyState === WebSocket.CLOSED) {
        throw new Error('WebSocket连接已关闭');
      }

      const closeInfo = {
        code,
        reason,
        wasClean: true,
        closedAt: new Date().toISOString(),
        previousState: websocket.readyState
      };

      // 关闭连接
      websocket.close(code, reason);

      this.setOutputValue('closeInfo', closeInfo);

      return 'closed';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * TCP套接字节点（模拟实现）
 */
export class TCPSocketNode extends FlowNode {
  private socket: any = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['connected', 'dataReceived', 'closed', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'TCP套接字';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'TCP套接字通信';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'host',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '主机地址',
      defaultValue: 'localhost'
    });

    this.addInput({
      name: 'port',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '端口号',
      defaultValue: 8080
    });

    this.addInput({
      name: 'action',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作（connect/send/close）',
      defaultValue: 'connect'
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '发送数据',
      defaultValue: ''
    });

    // 输出
    this.addOutput({
      name: 'socket',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'TCP套接字对象'
    });

    this.addOutput({
      name: 'receivedData',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '接收到的数据'
    });

    this.addOutput({
      name: 'connectionInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '连接信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const host = inputs.host || 'localhost';
      const port = inputs.port || 8080;
      const action = inputs.action || 'connect';
      const data = inputs.data || '';

      switch (action.toLowerCase()) {
        case 'connect':
          return await this.connect(host, port);

        case 'send':
          return await this.send(data);

        case 'close':
          return await this.close();

        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async connect(host: string, port: number): Promise<string> {
    // 模拟TCP连接
    this.socket = {
      id: `tcp_${Date.now()}`,
      host,
      port,
      connected: true,
      connectedAt: new Date().toISOString()
    };

    const connectionInfo = {
      host,
      port,
      protocol: 'TCP',
      connected: true,
      connectedAt: this.socket.connectedAt
    };

    this.setOutputValue('socket', this.socket);
    this.setOutputValue('connectionInfo', connectionInfo);

    return 'connected';
  }

  private async send(data: string): Promise<string> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('TCP套接字未连接');
    }

    // 模拟数据发送和接收
    const response = `Echo: ${data}`;

    this.setOutputValue('receivedData', response);

    return 'dataReceived';
  }

  private async close(): Promise<string> {
    if (this.socket) {
      this.socket.connected = false;
      this.socket.closedAt = new Date().toISOString();
      this.socket = null;
    }

    return 'closed';
  }
}

/**
 * UDP套接字节点（模拟实现）
 */
export class UDPSocketNode extends FlowNode {
  private socket: any = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['bound', 'sent', 'received', 'closed', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'UDP套接字';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'UDP套接字通信';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'host',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '主机地址',
      defaultValue: 'localhost'
    });

    this.addInput({
      name: 'port',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '端口号',
      defaultValue: 8080
    });

    this.addInput({
      name: 'action',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作（bind/send/close）',
      defaultValue: 'bind'
    });

    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '发送消息',
      defaultValue: ''
    });

    this.addInput({
      name: 'targetHost',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标主机',
      defaultValue: 'localhost'
    });

    this.addInput({
      name: 'targetPort',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '目标端口',
      defaultValue: 8081
    });

    // 输出
    this.addOutput({
      name: 'socket',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'UDP套接字对象'
    });

    this.addOutput({
      name: 'receivedMessage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '接收到的消息'
    });

    this.addOutput({
      name: 'senderInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '发送者信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const host = inputs.host || 'localhost';
      const port = inputs.port || 8080;
      const action = inputs.action || 'bind';
      const message = inputs.message || '';
      const targetHost = inputs.targetHost || 'localhost';
      const targetPort = inputs.targetPort || 8081;

      switch (action.toLowerCase()) {
        case 'bind':
          return await this.bind(host, port);

        case 'send':
          return await this.send(message, targetHost, targetPort);

        case 'close':
          return await this.close();

        default:
          throw new Error(`不支持的操作: ${action}`);
      }
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async bind(host: string, port: number): Promise<string> {
    // 模拟UDP绑定
    this.socket = {
      id: `udp_${Date.now()}`,
      host,
      port,
      bound: true,
      boundAt: new Date().toISOString()
    };

    this.setOutputValue('socket', this.socket);

    return 'bound';
  }

  private async send(message: string, targetHost: string, targetPort: number): Promise<string> {
    if (!this.socket || !this.socket.bound) {
      throw new Error('UDP套接字未绑定');
    }

    // 模拟UDP发送
    const senderInfo = {
      targetHost,
      targetPort,
      messageSize: message.length,
      sentAt: new Date().toISOString()
    };

    this.setOutputValue('senderInfo', senderInfo);

    return 'sent';
  }

  private async close(): Promise<string> {
    if (this.socket) {
      this.socket.bound = false;
      this.socket.closedAt = new Date().toISOString();
      this.socket = null;
    }

    return 'closed';
  }
}

/**
 * 网络状态检测节点
 */
export class NetworkStatusNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['online', 'offline', 'checked']
    });

    if (!this.metadata.name) {
      this.metadata.name = '网络状态';
    }
    if (!this.metadata.description) {
      this.metadata.description = '网络状态检测';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'checkInterval',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '检查间隔（毫秒）',
      defaultValue: 5000
    });

    this.addInput({
      name: 'testUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '测试URL',
      defaultValue: 'https://www.google.com'
    });

    // 输出
    this.addOutput({
      name: 'isOnline',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否在线'
    });

    this.addOutput({
      name: 'connectionType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '连接类型'
    });

    this.addOutput({
      name: 'networkInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '网络信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const checkInterval = inputs.checkInterval || 5000;
      const testUrl = inputs.testUrl || 'https://www.google.com';

      // 检查浏览器在线状态
      const isOnline = navigator.onLine;

      // 获取网络连接信息（如果支持）
      const connection = (navigator as any).connection ||
                       (navigator as any).mozConnection ||
                       (navigator as any).webkitConnection;

      const connectionType = connection ? connection.effectiveType || connection.type : 'unknown';

      const networkInfo = {
        online: isOnline,
        connectionType,
        downlink: connection?.downlink || 0,
        rtt: connection?.rtt || 0,
        saveData: connection?.saveData || false,
        checkedAt: new Date().toISOString()
      };

      this.setOutputValue('isOnline', isOnline);
      this.setOutputValue('connectionType', connectionType);
      this.setOutputValue('networkInfo', networkInfo);

      if (isOnline) {
        return 'online';
      } else {
        return 'offline';
      }
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'checked';
    }
  }
}

/**
 * Ping节点
 */
export class PingNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'timeout', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'Ping测试';
    }
    if (!this.metadata.description) {
      this.metadata.description = '网络延迟测试';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'host',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标主机',
      defaultValue: 'www.google.com'
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '超时时间（毫秒）',
      defaultValue: 5000
    });

    this.addInput({
      name: 'count',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Ping次数',
      defaultValue: 1
    });

    // 输出
    this.addOutput({
      name: 'latency',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '延迟（毫秒）'
    });

    this.addOutput({
      name: 'averageLatency',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '平均延迟（毫秒）'
    });

    this.addOutput({
      name: 'pingResults',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: 'Ping结果列表'
    });

    this.addOutput({
      name: 'statistics',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'Ping统计信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const host = inputs.host || 'www.google.com';
      const timeout = inputs.timeout || 5000;
      const count = inputs.count || 1;

      const pingResults: any[] = [];
      let totalLatency = 0;
      let successCount = 0;

      for (let i = 0; i < count; i++) {
        try {
          const startTime = Date.now();

          // 使用fetch进行简单的网络测试（模拟ping）
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeout);

          await fetch(`https://${host}`, {
            method: 'HEAD',
            mode: 'no-cors',
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          const latency = Date.now() - startTime;
          totalLatency += latency;
          successCount++;

          pingResults.push({
            sequence: i + 1,
            latency,
            success: true,
            timestamp: new Date().toISOString()
          });

        } catch (error) {
          pingResults.push({
            sequence: i + 1,
            latency: -1,
            success: false,
            error: error.name === 'AbortError' ? 'timeout' : 'failed',
            timestamp: new Date().toISOString()
          });
        }

        // 在多次ping之间添加小延迟
        if (i < count - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const averageLatency = successCount > 0 ? totalLatency / successCount : -1;
      const packetLoss = ((count - successCount) / count) * 100;

      const statistics = {
        host,
        packetsTransmitted: count,
        packetsReceived: successCount,
        packetLoss: `${packetLoss.toFixed(1)}%`,
        averageLatency: averageLatency.toFixed(2),
        minLatency: successCount > 0 ? Math.min(...pingResults.filter(r => r.success).map(r => r.latency)) : -1,
        maxLatency: successCount > 0 ? Math.max(...pingResults.filter(r => r.success).map(r => r.latency)) : -1
      };

      this.setOutputValue('latency', pingResults[pingResults.length - 1]?.latency || -1);
      this.setOutputValue('averageLatency', averageLatency);
      this.setOutputValue('pingResults', pingResults);
      this.setOutputValue('statistics', statistics);

      if (successCount === 0) {
        return 'timeout';
      } else if (successCount < count) {
        return 'success'; // 部分成功也算成功
      } else {
        return 'success';
      }

    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 注册网络通信节点
 */
export function registerNetworkNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'network/websocket/connect',
    name: 'WebSocket连接',
    category: '网络节点',
    description: '建立WebSocket连接',
    factory: (options: FlowNodeOptions) => new WebSocketConnectNode(options)
  });

  registry.registerNodeType({
    type: 'network/websocket/send',
    name: 'WebSocket发送',
    category: '网络节点',
    description: '发送WebSocket消息',
    factory: (options: FlowNodeOptions) => new WebSocketSendNode(options)
  });

  registry.registerNodeType({
    type: 'network/websocket/receive',
    name: 'WebSocket接收',
    category: '网络节点',
    description: '接收WebSocket消息',
    factory: (options: FlowNodeOptions) => new WebSocketReceiveNode(options)
  });

  registry.registerNodeType({
    type: 'network/websocket/close',
    name: 'WebSocket关闭',
    category: '网络节点',
    description: '关闭WebSocket连接',
    factory: (options: FlowNodeOptions) => new WebSocketCloseNode(options)
  });

  registry.registerNodeType({
    type: 'network/tcp/socket',
    name: 'TCP套接字',
    category: '网络节点',
    description: 'TCP套接字通信',
    factory: (options: FlowNodeOptions) => new TCPSocketNode(options)
  });

  registry.registerNodeType({
    type: 'network/udp/socket',
    name: 'UDP套接字',
    category: '网络节点',
    description: 'UDP套接字通信',
    factory: (options: FlowNodeOptions) => new UDPSocketNode(options)
  });

  registry.registerNodeType({
    type: 'network/status',
    name: '网络状态',
    category: '网络节点',
    description: '网络状态检测',
    factory: (options: FlowNodeOptions) => new NetworkStatusNode(options)
  });

  registry.registerNodeType({
    type: 'network/ping',
    name: 'Ping测试',
    category: '网络节点',
    description: '网络延迟测试',
    factory: (options: FlowNodeOptions) => new PingNode(options)
  });
}
