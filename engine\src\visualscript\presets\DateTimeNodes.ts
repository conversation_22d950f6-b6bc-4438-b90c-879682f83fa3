/**
 * 时间日期节点
 * 提供时间获取、格式化、计算、比较等功能
 */

import { Node, SocketType, SocketDirection, NodeOptions } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 获取当前时间节点
 */
export class GetCurrentTimeNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'format',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '时间格式 (timestamp/iso/date)',
      defaultValue: 'timestamp',
      optional: true
    });

    this.addOutput({
      name: 'time',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '当前时间'
    });

    this.addOutput({
      name: 'timestamp',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '时间戳（毫秒）'
    });

    this.addOutput({
      name: 'iso',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: 'ISO格式时间字符串'
    });
  }

  protected executeImpl(): any {
    const format = this.getInputValue('format') || 'timestamp';
    const now = new Date();
    const timestamp = now.getTime();
    const iso = now.toISOString();

    let time: any;
    switch (format) {
      case 'timestamp':
        time = timestamp;
        break;
      case 'iso':
        time = iso;
        break;
      case 'date':
        time = now;
        break;
      default:
        time = timestamp;
    }

    this.setOutputValue('time', time);
    this.setOutputValue('timestamp', timestamp);
    this.setOutputValue('iso', iso);

    return time;
  }
}

/**
 * 时间格式化节点
 */
export class FormatTimeNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'time',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '时间（时间戳、Date对象或ISO字符串）',
      defaultValue: Date.now()
    });

    this.addInput({
      name: 'format',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '格式字符串 (YYYY-MM-DD HH:mm:ss)',
      defaultValue: 'YYYY-MM-DD HH:mm:ss'
    });

    this.addInput({
      name: 'locale',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '地区设置',
      defaultValue: 'zh-CN',
      optional: true
    });

    this.addOutput({
      name: 'formatted',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '格式化后的时间字符串'
    });
  }

  protected executeImpl(): any {
    const time = this.getInputValue('time');
    const format = this.getInputValue('format') || 'YYYY-MM-DD HH:mm:ss';
    const locale = this.getInputValue('locale') || 'zh-CN';

    let date: Date;

    // 转换输入为Date对象
    if (time instanceof Date) {
      date = time;
    } else if (typeof time === 'number') {
      date = new Date(time);
    } else if (typeof time === 'string') {
      date = new Date(time);
    } else {
      date = new Date();
    }

    if (isNaN(date.getTime())) {
      this.setOutputValue('formatted', 'Invalid Date');
      return 'Invalid Date';
    }

    const formatted = this.formatDate(date, format, locale);
    this.setOutputValue('formatted', formatted);

    return formatted;
  }

  private formatDate(date: Date, format: string, locale: string): string {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();
    const milliseconds = date.getMilliseconds();

    const pad = (num: number, length: number = 2): string => {
      return num.toString().padStart(length, '0');
    };

    let result = format;
    result = result.replace(/YYYY/g, year.toString());
    result = result.replace(/YY/g, year.toString().slice(-2));
    result = result.replace(/MM/g, pad(month));
    result = result.replace(/M/g, month.toString());
    result = result.replace(/DD/g, pad(day));
    result = result.replace(/D/g, day.toString());
    result = result.replace(/HH/g, pad(hours));
    result = result.replace(/H/g, hours.toString());
    result = result.replace(/mm/g, pad(minutes));
    result = result.replace(/m/g, minutes.toString());
    result = result.replace(/ss/g, pad(seconds));
    result = result.replace(/s/g, seconds.toString());
    result = result.replace(/SSS/g, pad(milliseconds, 3));

    return result;
  }
}

/**
 * 时间计算节点
 */
export class TimeCalculationNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'time',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '基准时间',
      defaultValue: Date.now()
    });

    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '操作类型 (add/subtract)',
      defaultValue: 'add'
    });

    this.addInput({
      name: 'amount',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '数量',
      defaultValue: 1
    });

    this.addInput({
      name: 'unit',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '单位 (milliseconds/seconds/minutes/hours/days/weeks/months/years)',
      defaultValue: 'days'
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '计算结果（时间戳）'
    });

    this.addOutput({
      name: 'date',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '计算结果（Date对象）'
    });
  }

  protected executeImpl(): any {
    const time = this.getInputValue('time');
    const operation = this.getInputValue('operation') || 'add';
    const amount = this.getInputValue('amount') || 1;
    const unit = this.getInputValue('unit') || 'days';

    let date: Date;

    // 转换输入为Date对象
    if (time instanceof Date) {
      date = new Date(time);
    } else if (typeof time === 'number') {
      date = new Date(time);
    } else if (typeof time === 'string') {
      date = new Date(time);
    } else {
      date = new Date();
    }

    if (isNaN(date.getTime())) {
      this.setOutputValue('result', 0);
      this.setOutputValue('date', null);
      return 0;
    }

    // 计算时间偏移量（毫秒）
    let offset = 0;
    switch (unit) {
      case 'milliseconds':
        offset = amount;
        break;
      case 'seconds':
        offset = amount * 1000;
        break;
      case 'minutes':
        offset = amount * 60 * 1000;
        break;
      case 'hours':
        offset = amount * 60 * 60 * 1000;
        break;
      case 'days':
        offset = amount * 24 * 60 * 60 * 1000;
        break;
      case 'weeks':
        offset = amount * 7 * 24 * 60 * 60 * 1000;
        break;
      case 'months':
        // 月份计算比较复杂，使用Date的setMonth方法
        if (operation === 'add') {
          date.setMonth(date.getMonth() + amount);
        } else {
          date.setMonth(date.getMonth() - amount);
        }
        const result = date.getTime();
        this.setOutputValue('result', result);
        this.setOutputValue('date', date);
        return result;
      case 'years':
        // 年份计算
        if (operation === 'add') {
          date.setFullYear(date.getFullYear() + amount);
        } else {
          date.setFullYear(date.getFullYear() - amount);
        }
        const yearResult = date.getTime();
        this.setOutputValue('result', yearResult);
        this.setOutputValue('date', date);
        return yearResult;
      default:
        offset = amount * 24 * 60 * 60 * 1000; // 默认为天
    }

    // 应用偏移量
    if (operation === 'subtract') {
      offset = -offset;
    }

    const resultTime = date.getTime() + offset;
    const resultDate = new Date(resultTime);

    this.setOutputValue('result', resultTime);
    this.setOutputValue('date', resultDate);

    return resultTime;
  }
}

/**
 * 时间比较节点
 */
export class TimeComparisonNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'time1',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '第一个时间',
      defaultValue: Date.now()
    });

    this.addInput({
      name: 'time2',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '第二个时间',
      defaultValue: Date.now()
    });

    this.addOutput({
      name: 'equal',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否相等'
    });

    this.addOutput({
      name: 'greater',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: 'time1是否大于time2'
    });

    this.addOutput({
      name: 'less',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: 'time1是否小于time2'
    });

    this.addOutput({
      name: 'difference',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '时间差（毫秒）'
    });
  }

  protected executeImpl(): any {
    const time1 = this.getInputValue('time1');
    const time2 = this.getInputValue('time2');

    const timestamp1 = this.toTimestamp(time1);
    const timestamp2 = this.toTimestamp(time2);

    const equal = timestamp1 === timestamp2;
    const greater = timestamp1 > timestamp2;
    const less = timestamp1 < timestamp2;
    const difference = timestamp1 - timestamp2;

    this.setOutputValue('equal', equal);
    this.setOutputValue('greater', greater);
    this.setOutputValue('less', less);
    this.setOutputValue('difference', difference);

    return { equal, greater, less, difference };
  }

  private toTimestamp(time: any): number {
    if (time instanceof Date) {
      return time.getTime();
    } else if (typeof time === 'number') {
      return time;
    } else if (typeof time === 'string') {
      return new Date(time).getTime();
    }
    return 0;
  }
}

/**
 * 定时器节点
 */
export class TimerNode extends AsyncNode {
  private timerId: number | null = null;

  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '启动定时器'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止定时器'
    });

    this.addInput({
      name: 'delay',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '延迟时间（毫秒）',
      defaultValue: 1000
    });

    this.addInput({
      name: 'repeat',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否重复执行',
      defaultValue: false,
      optional: true
    });

    this.addOutput({
      name: 'tick',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '定时器触发时执行'
    });

    this.addOutput({
      name: 'started',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '定时器启动时触发'
    });

    this.addOutput({
      name: 'stopped',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '定时器停止时触发'
    });
  }

  protected async executeImpl(): Promise<any> {
    const inputName = this.getCurrentInputName();

    if (inputName === 'start') {
      return this.startTimer();
    } else if (inputName === 'stop') {
      return this.stopTimer();
    }

    return null;
  }

  private startTimer(): void {
    const delay = this.getInputValue('delay') || 1000;
    const repeat = this.getInputValue('repeat') || false;

    this.stopTimer(); // 停止现有定时器

    if (repeat) {
      this.timerId = window.setInterval(() => {
        this.triggerOutput('tick');
      }, delay);
    } else {
      this.timerId = window.setTimeout(() => {
        this.triggerOutput('tick');
        this.timerId = null;
      }, delay);
    }

    this.triggerOutput('started');
  }

  private stopTimer(): void {
    if (this.timerId !== null) {
      if (this.getInputValue('repeat')) {
        clearInterval(this.timerId);
      } else {
        clearTimeout(this.timerId);
      }
      this.timerId = null;
      this.triggerOutput('stopped');
    }
  }

  public dispose(): void {
    this.stopTimer();
    super.dispose();
  }
}

/**
 * 日期解析节点
 */
export class DateParseNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'dateString',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '日期字符串',
      defaultValue: ''
    });

    this.addInput({
      name: 'format',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '日期格式',
      defaultValue: 'auto',
      optional: true
    });

    this.addOutput({
      name: 'date',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '解析后的Date对象'
    });

    this.addOutput({
      name: 'timestamp',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '时间戳'
    });

    this.addOutput({
      name: 'valid',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否解析成功'
    });
  }

  protected executeImpl(): any {
    const dateString = this.getInputValue('dateString');
    const format = this.getInputValue('format') || 'auto';

    if (!dateString) {
      this.setOutputValue('date', null);
      this.setOutputValue('timestamp', 0);
      this.setOutputValue('valid', false);
      return null;
    }

    try {
      let date: Date;

      if (format === 'auto') {
        date = new Date(dateString);
      } else {
        // 这里可以实现更复杂的格式解析
        date = new Date(dateString);
      }

      const valid = !isNaN(date.getTime());
      const timestamp = valid ? date.getTime() : 0;

      this.setOutputValue('date', valid ? date : null);
      this.setOutputValue('timestamp', timestamp);
      this.setOutputValue('valid', valid);

      return valid ? date : null;
    } catch (error) {
      this.setOutputValue('date', null);
      this.setOutputValue('timestamp', 0);
      this.setOutputValue('valid', false);
      return null;
    }
  }
}

/**
 * 注册时间日期节点
 */
export function registerDateTimeNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'datetime/current',
    name: '获取当前时间',
    category: '时间日期节点',
    description: '获取当前系统时间',
    factory: (options: NodeOptions) => new GetCurrentTimeNode(options)
  });

  registry.registerNodeType({
    type: 'datetime/format',
    name: '时间格式化',
    category: '时间日期节点',
    description: '格式化时间显示',
    factory: (options: NodeOptions) => new FormatTimeNode(options)
  });

  registry.registerNodeType({
    type: 'datetime/calculate',
    name: '时间计算',
    category: '时间日期节点',
    description: '时间加减运算',
    factory: (options: NodeOptions) => new TimeCalculationNode(options)
  });

  registry.registerNodeType({
    type: 'datetime/compare',
    name: '时间比较',
    category: '时间日期节点',
    description: '比较两个时间',
    factory: (options: NodeOptions) => new TimeComparisonNode(options)
  });

  registry.registerNodeType({
    type: 'datetime/timer',
    name: '定时器',
    category: '时间日期节点',
    description: '创建定时器',
    factory: (options: NodeOptions) => new TimerNode(options)
  });

  registry.registerNodeType({
    type: 'datetime/parse',
    name: '日期解析',
    category: '时间日期节点',
    description: '解析日期字符串',
    factory: (options: NodeOptions) => new DateParseNode(options)
  });
}
