/**
 * 物理系统节点
 * 提供物理刚体、力学、碰撞检测、约束等功能
 */

import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 物理刚体创建节点
 */
export class PhysicsBodyCreateNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['created', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '创建物理刚体';
    }
    if (!this.metadata.description) {
      this.metadata.description = '创建物理刚体';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'physicsWorld',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '物理世界'
    });

    this.addInput({
      name: 'bodyType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '刚体类型（static/dynamic/kinematic）',
      defaultValue: 'dynamic'
    });

    this.addInput({
      name: 'shape',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '形状类型（box/sphere/cylinder/plane）',
      defaultValue: 'box'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '位置 {x, y, z}',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    this.addInput({
      name: 'rotation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '旋转 {x, y, z, w}',
      defaultValue: { x: 0, y: 0, z: 0, w: 1 }
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '尺寸 {x, y, z}',
      defaultValue: { x: 1, y: 1, z: 1 }
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '物理材质'
    });

    // 输出
    this.addOutput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '物理刚体'
    });

    this.addOutput({
      name: 'bodyId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '刚体ID'
    });

    this.addOutput({
      name: 'bodyInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '刚体信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const physicsWorld = inputs.physicsWorld;
      const bodyType = inputs.bodyType || 'dynamic';
      const shape = inputs.shape || 'box';
      const position = inputs.position || { x: 0, y: 0, z: 0 };
      const rotation = inputs.rotation || { x: 0, y: 0, z: 0, w: 1 };
      const size = inputs.size || { x: 1, y: 1, z: 1 };
      const mass = inputs.mass || 1.0;
      const material = inputs.material;

      if (!physicsWorld) {
        throw new Error('物理世界不能为空');
      }

      // 创建物理刚体（模拟实现）
      const bodyId = `body_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const body = {
        id: bodyId,
        type: bodyType,
        shape,
        position: { ...position },
        rotation: { ...rotation },
        size: { ...size },
        mass: bodyType === 'static' ? 0 : mass,
        velocity: { x: 0, y: 0, z: 0 },
        angularVelocity: { x: 0, y: 0, z: 0 },
        material: material || this.getDefaultMaterial(),
        active: true,
        createdAt: new Date().toISOString()
      };

      // 添加到物理世界
      if (physicsWorld.addBody) {
        physicsWorld.addBody(body);
      }

      const bodyInfo = {
        id: bodyId,
        type: bodyType,
        shape,
        mass: body.mass,
        hasCollision: true,
        createdAt: body.createdAt
      };

      this.setOutputValue('body', body);
      this.setOutputValue('bodyId', bodyId);
      this.setOutputValue('bodyInfo', bodyInfo);
      
      return 'created';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private getDefaultMaterial(): any {
    return {
      friction: 0.4,
      restitution: 0.3,
      density: 1.0,
      name: 'default'
    };
  }
}

/**
 * 物理刚体销毁节点
 */
export class PhysicsBodyDestroyNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['destroyed', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '销毁物理刚体';
    }
    if (!this.metadata.description) {
      this.metadata.description = '销毁物理刚体';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'physicsWorld',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '物理世界'
    });

    this.addInput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要销毁的物理刚体'
    });

    this.addInput({
      name: 'bodyId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '刚体ID（可选）'
    });

    // 输出
    this.addOutput({
      name: 'destroyedId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '已销毁的刚体ID'
    });

    this.addOutput({
      name: 'destroyInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '销毁信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const physicsWorld = inputs.physicsWorld;
      const body = inputs.body;
      const bodyId = inputs.bodyId;

      if (!physicsWorld) {
        throw new Error('物理世界不能为空');
      }

      let targetBody = body;
      let targetId = bodyId;

      // 如果提供了bodyId但没有body对象，尝试从物理世界中查找
      if (!targetBody && targetId && physicsWorld.getBody) {
        targetBody = physicsWorld.getBody(targetId);
      }

      // 如果有body对象但没有ID，从body对象获取ID
      if (targetBody && !targetId) {
        targetId = targetBody.id;
      }

      if (!targetBody && !targetId) {
        throw new Error('必须提供刚体对象或刚体ID');
      }

      // 从物理世界中移除刚体
      if (physicsWorld.removeBody) {
        physicsWorld.removeBody(targetBody || targetId);
      }

      const destroyInfo = {
        destroyedId: targetId,
        bodyType: targetBody?.type || 'unknown',
        destroyedAt: new Date().toISOString()
      };

      this.setOutputValue('destroyedId', targetId);
      this.setOutputValue('destroyInfo', destroyInfo);
      
      return 'destroyed';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 物理力施加节点
 */
export class PhysicsForceApplyNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['applied', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '施加物理力';
    }
    if (!this.metadata.description) {
      this.metadata.description = '施加物理力';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '物理刚体'
    });

    this.addInput({
      name: 'force',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '力向量 {x, y, z}',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    this.addInput({
      name: 'point',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '作用点 {x, y, z}（可选）'
    });

    this.addInput({
      name: 'forceMode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '力模式（force/impulse/acceleration/velocityChange）',
      defaultValue: 'force'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒，0表示瞬时）',
      defaultValue: 0
    });

    // 输出
    this.addOutput({
      name: 'appliedForce',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '已施加的力信息'
    });

    this.addOutput({
      name: 'resultingVelocity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '结果速度'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const body = inputs.body;
      const force = inputs.force || { x: 0, y: 0, z: 0 };
      const point = inputs.point;
      const forceMode = inputs.forceMode || 'force';
      const duration = inputs.duration || 0;

      if (!body) {
        throw new Error('物理刚体不能为空');
      }

      if (body.type === 'static') {
        throw new Error('静态刚体不能施加力');
      }

      // 模拟力的施加
      const appliedForce = {
        force: { ...force },
        point: point ? { ...point } : null,
        mode: forceMode,
        duration,
        appliedAt: new Date().toISOString()
      };

      // 计算结果速度（简化计算）
      const mass = body.mass || 1.0;
      let deltaVelocity = { x: 0, y: 0, z: 0 };

      switch (forceMode) {
        case 'force':
          // F = ma, a = F/m, Δv = a*Δt (假设Δt = 1/60秒)
          const dt = duration > 0 ? duration : 1/60;
          deltaVelocity = {
            x: (force.x / mass) * dt,
            y: (force.y / mass) * dt,
            z: (force.z / mass) * dt
          };
          break;

        case 'impulse':
          // 冲量 = mΔv, Δv = impulse/m
          deltaVelocity = {
            x: force.x / mass,
            y: force.y / mass,
            z: force.z / mass
          };
          break;

        case 'acceleration':
          // 直接施加加速度
          const accelDt = duration > 0 ? duration : 1/60;
          deltaVelocity = {
            x: force.x * accelDt,
            y: force.y * accelDt,
            z: force.z * accelDt
          };
          break;

        case 'velocityChange':
          // 直接改变速度
          deltaVelocity = { ...force };
          break;
      }

      // 更新刚体速度
      body.velocity = {
        x: body.velocity.x + deltaVelocity.x,
        y: body.velocity.y + deltaVelocity.y,
        z: body.velocity.z + deltaVelocity.z
      };

      this.setOutputValue('appliedForce', appliedForce);
      this.setOutputValue('resultingVelocity', { ...body.velocity });
      
      return 'applied';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 物理射线检测节点
 */
export class PhysicsRaycastNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['hit', 'miss', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '物理射线检测';
    }
    if (!this.metadata.description) {
      this.metadata.description = '射线检测';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'physicsWorld',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '物理世界'
    });

    this.addInput({
      name: 'origin',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '射线起点 {x, y, z}',
      defaultValue: { x: 0, y: 0, z: 0 }
    });

    this.addInput({
      name: 'direction',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '射线方向 {x, y, z}',
      defaultValue: { x: 0, y: 0, z: -1 }
    });

    this.addInput({
      name: 'maxDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大距离',
      defaultValue: 100
    });

    this.addInput({
      name: 'layerMask',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '层掩码',
      defaultValue: -1
    });

    this.addInput({
      name: 'includeStatic',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '包含静态刚体',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'hitInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '命中信息'
    });

    this.addOutput({
      name: 'hitPoint',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '命中点'
    });

    this.addOutput({
      name: 'hitNormal',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '命中法线'
    });

    this.addOutput({
      name: 'hitDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '命中距离'
    });

    this.addOutput({
      name: 'hitBody',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '命中的刚体'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const physicsWorld = inputs.physicsWorld;
      const origin = inputs.origin || { x: 0, y: 0, z: 0 };
      const direction = inputs.direction || { x: 0, y: 0, z: -1 };
      const maxDistance = inputs.maxDistance || 100;
      const layerMask = inputs.layerMask || -1;
      const includeStatic = inputs.includeStatic !== false;

      if (!physicsWorld) {
        throw new Error('物理世界不能为空');
      }

      // 标准化方向向量
      const dirLength = Math.sqrt(direction.x * direction.x + direction.y * direction.y + direction.z * direction.z);
      if (dirLength === 0) {
        throw new Error('射线方向不能为零向量');
      }

      const normalizedDirection = {
        x: direction.x / dirLength,
        y: direction.y / dirLength,
        z: direction.z / dirLength
      };

      // 执行射线检测
      const hitResult = this.performRaycast(physicsWorld, origin, normalizedDirection, maxDistance, includeStatic);

      if (hitResult) {
        const hitInfo = {
          origin: { ...origin },
          direction: { ...normalizedDirection },
          maxDistance,
          hitBody: {
            id: hitResult.body.id,
            type: hitResult.body.type,
            position: { ...hitResult.body.position }
          },
          hitPoint: { ...hitResult.point },
          hitNormal: { ...hitResult.normal },
          distance: hitResult.distance,
          raycastAt: new Date().toISOString()
        };

        this.setOutputValue('hitInfo', hitInfo);
        this.setOutputValue('hitPoint', hitResult.point);
        this.setOutputValue('hitNormal', hitResult.normal);
        this.setOutputValue('hitDistance', hitResult.distance);
        this.setOutputValue('hitBody', hitResult.body);

        return 'hit';
      } else {
        return 'miss';
      }
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private performRaycast(physicsWorld: any, origin: any, direction: any, maxDistance: number, includeStatic: boolean): any | null {
    // 模拟射线检测
    const bodies = physicsWorld.bodies || [];
    let closestHit: any = null;
    let closestDistance = maxDistance;

    for (const body of bodies) {
      if (!includeStatic && body.type === 'static') continue;

      // 简化的射线-球体相交检测
      const toBody = {
        x: body.position.x - origin.x,
        y: body.position.y - origin.y,
        z: body.position.z - origin.z
      };

      // 投影到射线方向
      const projection = toBody.x * direction.x + toBody.y * direction.y + toBody.z * direction.z;

      if (projection < 0 || projection > maxDistance) continue;

      // 计算最近点
      const closestPoint = {
        x: origin.x + direction.x * projection,
        y: origin.y + direction.y * projection,
        z: origin.z + direction.z * projection
      };

      // 计算距离
      const distanceToBody = Math.sqrt(
        Math.pow(closestPoint.x - body.position.x, 2) +
        Math.pow(closestPoint.y - body.position.y, 2) +
        Math.pow(closestPoint.z - body.position.z, 2)
      );

      // 简化：假设所有刚体都是球体
      const radius = Math.max(body.size.x, body.size.y, body.size.z) / 2;

      if (distanceToBody <= radius && projection < closestDistance) {
        // 计算实际命中点
        const hitDistance = projection - Math.sqrt(radius * radius - distanceToBody * distanceToBody);

        if (hitDistance >= 0) {
          const hitPoint = {
            x: origin.x + direction.x * hitDistance,
            y: origin.y + direction.y * hitDistance,
            z: origin.z + direction.z * hitDistance
          };

          // 计算法线（从球心指向命中点）
          const normal = {
            x: hitPoint.x - body.position.x,
            y: hitPoint.y - body.position.y,
            z: hitPoint.z - body.position.z
          };
          const normalLength = Math.sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);

          if (normalLength > 0) {
            normal.x /= normalLength;
            normal.y /= normalLength;
            normal.z /= normalLength;
          }

          closestHit = {
            body,
            point: hitPoint,
            normal,
            distance: hitDistance
          };
          closestDistance = hitDistance;
        }
      }
    }

    return closestHit;
  }
}

/**
 * 注册物理系统节点
 */
export function registerPhysicsNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'physics/body/create',
    name: '创建物理刚体',
    category: '物理节点',
    description: '创建物理刚体',
    factory: (options: FlowNodeOptions) => new PhysicsBodyCreateNode(options)
  });

  registry.registerNodeType({
    type: 'physics/body/destroy',
    name: '销毁物理刚体',
    category: '物理节点',
    description: '销毁物理刚体',
    factory: (options: FlowNodeOptions) => new PhysicsBodyDestroyNode(options)
  });

  registry.registerNodeType({
    type: 'physics/force/apply',
    name: '施加物理力',
    category: '物理节点',
    description: '施加物理力',
    factory: (options: FlowNodeOptions) => new PhysicsForceApplyNode(options)
  });

  registry.registerNodeType({
    type: 'physics/raycast',
    name: '物理射线检测',
    category: '物理节点',
    description: '射线检测',
    factory: (options: FlowNodeOptions) => new PhysicsRaycastNode(options)
  });
}
