/**
 * HTTP请求节点
 * 提供HTTP请求功能，支持GET、POST、PUT、DELETE等方法
 */

import { Node, SocketType, SocketDirection, NodeOptions } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * HTTP GET请求节点
 */
export class HTTPGetNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发HTTP GET请求'
    });

    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '请求URL',
      defaultValue: ''
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'params',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '查询参数',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '超时时间（毫秒）',
      defaultValue: 5000,
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求成功时触发'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求失败时触发'
    });

    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '响应数据'
    });

    this.addOutput({
      name: 'status',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '响应状态码'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '响应头'
    });
  }

  protected async executeImpl(): Promise<any> {
    const url = this.getInputValue('url');
    const headers = this.getInputValue('headers') || {};
    const params = this.getInputValue('params') || {};
    const timeout = this.getInputValue('timeout') || 5000;

    if (!url) {
      throw new Error('URL不能为空');
    }

    try {
      // 构建查询参数
      const urlObj = new URL(url);
      Object.keys(params).forEach(key => {
        urlObj.searchParams.append(key, params[key]);
      });

      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      // 发送HTTP GET请求
      const response = await fetch(urlObj.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // 解析响应
      const responseData = await response.json();
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      // 设置输出值
      this.setOutputValue('response', responseData);
      this.setOutputValue('status', response.status);
      this.setOutputValue('headers', responseHeaders);

      if (response.ok) {
        this.triggerOutput('success');
      } else {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      return responseData;
    } catch (error) {
      this.setOutputValue('response', null);
      this.setOutputValue('status', 0);
      this.setOutputValue('headers', {});
      this.triggerOutput('error');
      throw error;
    }
  }
}

/**
 * HTTP POST请求节点
 */
export class HTTPPostNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发HTTP POST请求'
    });

    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '请求URL',
      defaultValue: ''
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '请求数据',
      defaultValue: {}
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '超时时间（毫秒）',
      defaultValue: 5000,
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求成功时触发'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求失败时触发'
    });

    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '响应数据'
    });

    this.addOutput({
      name: 'status',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '响应状态码'
    });
  }

  protected async executeImpl(): Promise<any> {
    const url = this.getInputValue('url');
    const data = this.getInputValue('data') || {};
    const headers = this.getInputValue('headers') || {};
    const timeout = this.getInputValue('timeout') || 5000;

    if (!url) {
      throw new Error('URL不能为空');
    }

    try {
      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      // 发送HTTP POST请求
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify(data),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // 解析响应
      const responseData = await response.json();

      // 设置输出值
      this.setOutputValue('response', responseData);
      this.setOutputValue('status', response.status);

      if (response.ok) {
        this.triggerOutput('success');
      } else {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      return responseData;
    } catch (error) {
      this.setOutputValue('response', null);
      this.setOutputValue('status', 0);
      this.triggerOutput('error');
      throw error;
    }
  }
}

/**
 * HTTP DELETE请求节点
 */
export class HTTPDeleteNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发HTTP DELETE请求'
    });

    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '请求URL',
      defaultValue: ''
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求成功时触发'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求失败时触发'
    });

    this.addOutput({
      name: 'status',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '响应状态码'
    });
  }

  protected async executeImpl(): Promise<any> {
    const url = this.getInputValue('url');
    const headers = this.getInputValue('headers') || {};

    if (!url) {
      throw new Error('URL不能为空');
    }

    try {
      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      });

      this.setOutputValue('status', response.status);

      if (response.ok) {
        this.triggerOutput('success');
      } else {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      return response.status;
    } catch (error) {
      this.setOutputValue('status', 0);
      this.triggerOutput('error');
      throw error;
    }
  }
}

/**
 * HTTP请求头设置节点
 */
export class HTTPHeaderNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发设置请求头'
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '现有请求头',
      defaultValue: {}
    });

    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '请求头键名',
      defaultValue: ''
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '请求头值',
      defaultValue: ''
    });

    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '设置完成后触发'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '更新后的请求头'
    });
  }

  protected executeImpl(): any {
    const headers = { ...this.getInputValue('headers') } || {};
    const key = this.getInputValue('key');
    const value = this.getInputValue('value');

    if (key && value) {
      headers[key] = value;
    }

    this.setOutputValue('headers', headers);
    this.triggerOutput('flow');

    return headers;
  }
}

/**
 * HTTP认证节点
 */
export class HTTPAuthNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发设置认证'
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '现有请求头',
      defaultValue: {}
    });

    this.addInput({
      name: 'authType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '认证类型 (bearer/basic)',
      defaultValue: 'bearer'
    });

    this.addInput({
      name: 'token',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: 'Token或用户名',
      defaultValue: ''
    });

    this.addInput({
      name: 'password',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '密码（Basic认证时使用）',
      defaultValue: '',
      optional: true
    });

    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '设置完成后触发'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '包含认证信息的请求头'
    });
  }

  protected executeImpl(): any {
    const headers = { ...this.getInputValue('headers') } || {};
    const authType = this.getInputValue('authType').toLowerCase();
    const token = this.getInputValue('token');
    const password = this.getInputValue('password');

    if (authType === 'bearer' && token) {
      headers['Authorization'] = `Bearer ${token}`;
    } else if (authType === 'basic' && token && password) {
      const credentials = btoa(`${token}:${password}`);
      headers['Authorization'] = `Basic ${credentials}`;
    }

    this.setOutputValue('headers', headers);
    this.triggerOutput('flow');

    return headers;
  }
}

/**
 * 注册HTTP节点
 */
export function registerHTTPNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'http/request/get',
    name: 'HTTP GET请求',
    category: 'HTTP节点',
    description: '发送HTTP GET请求',
    factory: (options: NodeOptions) => new HTTPGetNode(options)
  });

  registry.registerNodeType({
    type: 'http/request/post',
    name: 'HTTP POST请求',
    category: 'HTTP节点',
    description: '发送HTTP POST请求',
    factory: (options: NodeOptions) => new HTTPPostNode(options)
  });

  registry.registerNodeType({
    type: 'http/request/put',
    name: 'HTTP PUT请求',
    category: 'HTTP节点',
    description: '发送HTTP PUT请求',
    factory: (options: NodeOptions) => new HTTPPutNode(options)
  });

  registry.registerNodeType({
    type: 'http/request/delete',
    name: 'HTTP DELETE请求',
    category: 'HTTP节点',
    description: '发送HTTP DELETE请求',
    factory: (options: NodeOptions) => new HTTPDeleteNode(options)
  });

  registry.registerNodeType({
    type: 'http/header/set',
    name: 'HTTP请求头设置',
    category: 'HTTP节点',
    description: '设置HTTP请求头',
    factory: (options: NodeOptions) => new HTTPHeaderNode(options)
  });

  registry.registerNodeType({
    type: 'http/auth/set',
    name: 'HTTP认证',
    category: 'HTTP节点',
    description: '设置HTTP认证信息',
    factory: (options: NodeOptions) => new HTTPAuthNode(options)
  });
}

/**
 * HTTP PUT请求节点
 */
export class HTTPPutNode extends AsyncNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发HTTP PUT请求'
    });

    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '请求URL',
      defaultValue: ''
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '请求数据',
      defaultValue: {}
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求成功时触发'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求失败时触发'
    });

    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '响应数据'
    });

    this.addOutput({
      name: 'status',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '响应状态码'
    });
  }

  protected async executeImpl(): Promise<any> {
    const url = this.getInputValue('url');
    const data = this.getInputValue('data') || {};
    const headers = this.getInputValue('headers') || {};

    if (!url) {
      throw new Error('URL不能为空');
    }

    try {
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify(data)
      });

      const responseData = await response.json();

      this.setOutputValue('response', responseData);
      this.setOutputValue('status', response.status);

      if (response.ok) {
        this.triggerOutput('success');
      } else {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      return responseData;
    } catch (error) {
      this.setOutputValue('response', null);
      this.setOutputValue('status', 0);
      this.triggerOutput('error');
      throw error;
    }
  }
}
