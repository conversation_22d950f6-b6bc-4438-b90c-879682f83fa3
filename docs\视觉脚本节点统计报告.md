# 视觉脚本系统节点统计报告

生成时间: 2025/6/24 20:52:31

## 总体统计

- **总节点数量**: 50
- **节点文件数量**: 8
- **节点类别数量**: 8

## 按类别统计

- **数学节点**: 10 个节点
- **核心节点**: 7 个节点
- **日期时间节点**: 6 个节点
- **HTTP节点**: 6 个节点
- **JSON节点**: 6 个节点
- **文件系统节点**: 5 个节点
- **图像处理节点**: 5 个节点
- **UI节点**: 5 个节点

## 按文件详细统计

### MathNodes (数学节点)

- **节点类数量**: 10
- **注册类型数量**: 10
- **节点类列表**:
  - AddNode
  - SubtractNode
  - MultiplyNode
  - DivideNode
  - PowerNode
  - SqrtNode
  - AbsNode
  - TrigonometricNode
  - RandomNode
  - VectorOperationNode
- **注册类型列表**:
  - math/basic/add
  - math/basic/subtract
  - math/basic/multiply
  - math/basic/divide
  - math/advanced/power
  - math/advanced/sqrt
  - math/advanced/abs
  - math/trigonometric
  - math/random
  - math/vector

### CoreNodes (核心节点)

- **节点类数量**: 7
- **注册类型数量**: 7
- **节点类列表**:
  - OnStartNode
  - OnUpdateNode
  - SequenceNode
  - BranchNode
  - DelayNode
  - ForLoopNode
  - WhileLoopNode
- **注册类型列表**:
  - core/events/onStart
  - core/events/onUpdate
  - core/flow/sequence
  - core/flow/branch
  - core/flow/delay
  - core/flow/forLoop
  - core/flow/whileLoop

### DateTimeNodes (日期时间节点)

- **节点类数量**: 6
- **注册类型数量**: 6
- **节点类列表**:
  - GetCurrentTimeNode
  - FormatTimeNode
  - TimeCalculationNode
  - TimeComparisonNode
  - TimerNode
  - DateParseNode
- **注册类型列表**:
  - datetime/current
  - datetime/format
  - datetime/calculate
  - datetime/compare
  - datetime/timer
  - datetime/parse

### HTTPNodes (HTTP节点)

- **节点类数量**: 6
- **注册类型数量**: 6
- **节点类列表**:
  - HTTPGetNode
  - HTTPPostNode
  - HTTPDeleteNode
  - HTTPHeaderNode
  - HTTPAuthNode
  - HTTPPutNode
- **注册类型列表**:
  - http/request/get
  - http/request/post
  - http/request/put
  - http/request/delete
  - http/header/set
  - http/auth/set

### JSONNodes (JSON节点)

- **节点类数量**: 6
- **注册类型数量**: 6
- **节点类列表**:
  - JSONParseNode
  - JSONStringifyNode
  - JSONPathNode
  - JSONMergeNode
  - JSONValidateNode
  - JSONTransformNode
- **注册类型列表**:
  - json/parse
  - json/stringify
  - json/path
  - json/merge
  - json/validate
  - json/transform

### FileSystemNodes (文件系统节点)

- **节点类数量**: 5
- **注册类型数量**: 5
- **节点类列表**:
  - ReadTextFileNode
  - WriteTextFileNode
  - FileExistsNode
  - CreateDirectoryNode
  - DeleteFileNode
- **注册类型列表**:
  - filesystem/file/readText
  - filesystem/file/writeText
  - filesystem/file/exists
  - filesystem/directory/create
  - filesystem/file/delete

### ImageProcessingNodes (图像处理节点)

- **节点类数量**: 5
- **注册类型数量**: 5
- **节点类列表**:
  - LoadImageNode
  - ResizeImageNode
  - ImageFilterNode
  - CropImageNode
  - RotateImageNode
- **注册类型列表**:
  - image/load
  - image/resize
  - image/filter
  - image/crop
  - image/rotate

### UINodes (UI节点)

- **节点类数量**: 5
- **注册类型数量**: 5
- **节点类列表**:
  - CreateButtonNode
  - CreateTextNode
  - CreateInputNode
  - CreateSliderNode
  - CreateImageNode
- **注册类型列表**:
  - ui/button/create
  - ui/text/create
  - ui/input/create
  - ui/slider/create
  - ui/image/create

## 所有节点列表

- **AbsNode** (数学节点 - MathNodes)
- **AddNode** (数学节点 - MathNodes)
- **BranchNode** (核心节点 - CoreNodes)
- **CreateButtonNode** (UI节点 - UINodes)
- **CreateDirectoryNode** (文件系统节点 - FileSystemNodes)
- **CreateImageNode** (UI节点 - UINodes)
- **CreateInputNode** (UI节点 - UINodes)
- **CreateSliderNode** (UI节点 - UINodes)
- **CreateTextNode** (UI节点 - UINodes)
- **CropImageNode** (图像处理节点 - ImageProcessingNodes)
- **DateParseNode** (日期时间节点 - DateTimeNodes)
- **DelayNode** (核心节点 - CoreNodes)
- **DeleteFileNode** (文件系统节点 - FileSystemNodes)
- **DivideNode** (数学节点 - MathNodes)
- **FileExistsNode** (文件系统节点 - FileSystemNodes)
- **ForLoopNode** (核心节点 - CoreNodes)
- **FormatTimeNode** (日期时间节点 - DateTimeNodes)
- **GetCurrentTimeNode** (日期时间节点 - DateTimeNodes)
- **HTTPAuthNode** (HTTP节点 - HTTPNodes)
- **HTTPDeleteNode** (HTTP节点 - HTTPNodes)
- **HTTPGetNode** (HTTP节点 - HTTPNodes)
- **HTTPHeaderNode** (HTTP节点 - HTTPNodes)
- **HTTPPostNode** (HTTP节点 - HTTPNodes)
- **HTTPPutNode** (HTTP节点 - HTTPNodes)
- **ImageFilterNode** (图像处理节点 - ImageProcessingNodes)
- **JSONMergeNode** (JSON节点 - JSONNodes)
- **JSONParseNode** (JSON节点 - JSONNodes)
- **JSONPathNode** (JSON节点 - JSONNodes)
- **JSONStringifyNode** (JSON节点 - JSONNodes)
- **JSONTransformNode** (JSON节点 - JSONNodes)
- **JSONValidateNode** (JSON节点 - JSONNodes)
- **LoadImageNode** (图像处理节点 - ImageProcessingNodes)
- **MultiplyNode** (数学节点 - MathNodes)
- **OnStartNode** (核心节点 - CoreNodes)
- **OnUpdateNode** (核心节点 - CoreNodes)
- **PowerNode** (数学节点 - MathNodes)
- **RandomNode** (数学节点 - MathNodes)
- **ReadTextFileNode** (文件系统节点 - FileSystemNodes)
- **ResizeImageNode** (图像处理节点 - ImageProcessingNodes)
- **RotateImageNode** (图像处理节点 - ImageProcessingNodes)
- **SequenceNode** (核心节点 - CoreNodes)
- **SqrtNode** (数学节点 - MathNodes)
- **SubtractNode** (数学节点 - MathNodes)
- **TimeCalculationNode** (日期时间节点 - DateTimeNodes)
- **TimeComparisonNode** (日期时间节点 - DateTimeNodes)
- **TimerNode** (日期时间节点 - DateTimeNodes)
- **TrigonometricNode** (数学节点 - MathNodes)
- **VectorOperationNode** (数学节点 - MathNodes)
- **WhileLoopNode** (核心节点 - CoreNodes)
- **WriteTextFileNode** (文件系统节点 - FileSystemNodes)
