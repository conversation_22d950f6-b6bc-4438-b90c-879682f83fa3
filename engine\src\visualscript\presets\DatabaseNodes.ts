/**
 * 数据库操作节点
 * 提供数据库连接、查询、操作等功能
 */

import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 数据库连接配置接口
 */
export interface DatabaseConfig {
  type: 'mysql' | 'postgresql' | 'mongodb' | 'sqlite';
  host?: string;
  port?: number;
  database: string;
  username?: string;
  password?: string;
  connectionString?: string;
  options?: Record<string, any>;
}

/**
 * 数据库连接节点
 */
export class DatabaseConnectNode extends FlowNode {
  private connection: any = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['connected', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库连接';
    }
    if (!this.metadata.description) {
      this.metadata.description = '建立数据库连接（支持MySQL、PostgreSQL、MongoDB）';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 数据库类型输入
    this.addInput({
      name: 'dbType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '数据库类型（mysql/postgresql/mongodb/sqlite）',
      defaultValue: 'mysql'
    });

    // 连接配置输入
    this.addInput({
      name: 'host',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '主机地址',
      defaultValue: 'localhost'
    });

    this.addInput({
      name: 'port',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '端口号',
      defaultValue: 3306
    });

    this.addInput({
      name: 'database',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '数据库名称',
      defaultValue: ''
    });

    this.addInput({
      name: 'username',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户名',
      defaultValue: ''
    });

    this.addInput({
      name: 'password',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '密码',
      defaultValue: ''
    });

    this.addInput({
      name: 'connectionString',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '连接字符串（可选）',
      defaultValue: ''
    });

    // 连接对象输出
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '数据库连接对象'
    });

    this.addOutput({
      name: 'connectionInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '连接信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const dbType = inputs.dbType || 'mysql';
      const host = inputs.host || 'localhost';
      const port = inputs.port || this.getDefaultPort(dbType);
      const database = inputs.database;
      const username = inputs.username;
      const password = inputs.password;
      const connectionString = inputs.connectionString;

      if (!database) {
        throw new Error('数据库名称不能为空');
      }

      // 构建连接配置
      const config: DatabaseConfig = {
        type: dbType as any,
        host,
        port,
        database,
        username,
        password,
        connectionString,
        options: {
          connectTimeout: 10000,
          acquireTimeout: 10000,
          timeout: 10000
        }
      };

      // 模拟数据库连接
      this.connection = await this.createConnection(config);

      const connectionInfo = {
        type: dbType,
        host,
        port,
        database,
        connected: true,
        connectedAt: new Date().toISOString()
      };

      this.setOutputValue('connection', this.connection);
      this.setOutputValue('connectionInfo', connectionInfo);
      
      return 'connected';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private getDefaultPort(dbType: string): number {
    const defaultPorts = {
      mysql: 3306,
      postgresql: 5432,
      mongodb: 27017,
      sqlite: 0
    };
    return defaultPorts[dbType] || 3306;
  }

  private async createConnection(config: DatabaseConfig): Promise<any> {
    // 模拟连接创建过程
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: `conn_${Date.now()}`,
          config,
          connected: true,
          query: this.mockQuery.bind(this),
          execute: this.mockExecute.bind(this),
          close: this.mockClose.bind(this)
        });
      }, 100);
    });
  }

  private async mockQuery(sql: string, params: any[] = []): Promise<any[]> {
    // 模拟查询结果
    return [
      { id: 1, name: '示例数据1', value: 100 },
      { id: 2, name: '示例数据2', value: 200 }
    ];
  }

  private async mockExecute(sql: string, params: any[] = []): Promise<{ affectedRows: number; insertId?: number }> {
    // 模拟执行结果
    return {
      affectedRows: 1,
      insertId: Math.floor(Math.random() * 1000) + 1
    };
  }

  private async mockClose(): Promise<void> {
    // 模拟关闭连接
    this.connection = null;
  }
}

/**
 * 数据库查询节点
 */
export class DatabaseQueryNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库查询';
    }
    if (!this.metadata.description) {
      this.metadata.description = '执行SELECT查询，支持复杂条件';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'query',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'SQL查询语句',
      defaultValue: 'SELECT * FROM table'
    });

    this.addInput({
      name: 'parameters',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '查询参数',
      defaultValue: []
    });

    this.addInput({
      name: 'limit',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '限制结果数量',
      defaultValue: 100
    });

    this.addInput({
      name: 'offset',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '偏移量',
      defaultValue: 0
    });

    // 输出
    this.addOutput({
      name: 'results',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '查询结果'
    });

    this.addOutput({
      name: 'rowCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '结果行数'
    });

    this.addOutput({
      name: 'totalCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '总记录数'
    });

    this.addOutput({
      name: 'executionTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '执行时间（毫秒）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    const startTime = Date.now();

    try {
      const connection = inputs.connection;
      const query = inputs.query;
      const parameters = inputs.parameters || [];
      const limit = inputs.limit || 100;
      const offset = inputs.offset || 0;

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      // 构建完整查询
      let fullQuery = query;
      if (limit > 0) {
        fullQuery += ` LIMIT ${limit}`;
        if (offset > 0) {
          fullQuery += ` OFFSET ${offset}`;
        }
      }

      // 执行查询
      const results = await connection.query(fullQuery, parameters);
      const executionTime = Date.now() - startTime;

      this.setOutputValue('results', results);
      this.setOutputValue('rowCount', results.length);
      this.setOutputValue('totalCount', results.length); // 在实际实现中应该是总数
      this.setOutputValue('executionTime', executionTime);

      return 'success';
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.setOutputValue('error', error.message);
      this.setOutputValue('executionTime', executionTime);
      return 'failed';
    }
  }
}

/**
 * 数据库插入节点
 */
export class DatabaseInsertNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库插入';
    }
    if (!this.metadata.description) {
      this.metadata.description = '执行INSERT操作，支持批量插入';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'table',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '表名',
      defaultValue: ''
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '插入数据（对象或对象数组）'
    });

    this.addInput({
      name: 'onDuplicate',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '重复处理方式（ignore/update/error）',
      defaultValue: 'error'
    });

    // 输出
    this.addOutput({
      name: 'insertId',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '插入ID'
    });

    this.addOutput({
      name: 'affectedRows',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '影响行数'
    });

    this.addOutput({
      name: 'insertedData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '插入的数据'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const connection = inputs.connection;
      const table = inputs.table;
      const data = inputs.data;
      const onDuplicate = inputs.onDuplicate || 'error';

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      if (!table) {
        throw new Error('表名不能为空');
      }

      if (!data) {
        throw new Error('插入数据不能为空');
      }

      // 构建插入语句
      const isArray = Array.isArray(data);
      const records = isArray ? data : [data];

      if (records.length === 0) {
        throw new Error('插入数据不能为空');
      }

      // 获取字段名
      const fields = Object.keys(records[0]);
      const placeholders = fields.map(() => '?').join(', ');

      let sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES `;

      if (isArray) {
        // 批量插入
        const valuePlaceholders = records.map(() => `(${placeholders})`).join(', ');
        sql += valuePlaceholders;

        // 处理重复键
        if (onDuplicate === 'ignore') {
          sql += ' ON DUPLICATE KEY UPDATE id=id';
        } else if (onDuplicate === 'update') {
          const updateFields = fields.map(field => `${field}=VALUES(${field})`).join(', ');
          sql += ` ON DUPLICATE KEY UPDATE ${updateFields}`;
        }
      } else {
        // 单条插入
        sql += `(${placeholders})`;

        if (onDuplicate === 'ignore') {
          sql = sql.replace('INSERT INTO', 'INSERT IGNORE INTO');
        }
      }

      // 准备参数
      const params = records.flatMap(record => fields.map(field => record[field]));

      // 执行插入
      const result = await connection.execute(sql, params);

      this.setOutputValue('insertId', result.insertId);
      this.setOutputValue('affectedRows', result.affectedRows);
      this.setOutputValue('insertedData', data);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 数据库更新节点
 */
export class DatabaseUpdateNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库更新';
    }
    if (!this.metadata.description) {
      this.metadata.description = '执行UPDATE操作，支持条件更新';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'table',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '表名',
      defaultValue: ''
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '更新数据'
    });

    this.addInput({
      name: 'where',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '更新条件'
    });

    this.addInput({
      name: 'limit',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '限制更新行数',
      defaultValue: 0
    });

    // 输出
    this.addOutput({
      name: 'affectedRows',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '影响行数'
    });

    this.addOutput({
      name: 'changedRows',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '实际更改行数'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const connection = inputs.connection;
      const table = inputs.table;
      const data = inputs.data;
      const where = inputs.where;
      const limit = inputs.limit || 0;

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      if (!table) {
        throw new Error('表名不能为空');
      }

      if (!data || Object.keys(data).length === 0) {
        throw new Error('更新数据不能为空');
      }

      // 构建更新语句
      const setFields = Object.keys(data).map(field => `${field} = ?`).join(', ');
      let sql = `UPDATE ${table} SET ${setFields}`;
      const params = Object.values(data);

      // 添加WHERE条件
      if (where && Object.keys(where).length > 0) {
        const whereFields = Object.keys(where).map(field => `${field} = ?`).join(' AND ');
        sql += ` WHERE ${whereFields}`;
        params.push(...Object.values(where));
      }

      // 添加LIMIT
      if (limit > 0) {
        sql += ` LIMIT ${limit}`;
      }

      // 执行更新
      const result = await connection.execute(sql, params);

      this.setOutputValue('affectedRows', result.affectedRows);
      this.setOutputValue('changedRows', result.changedRows || result.affectedRows);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 数据库删除节点
 */
export class DatabaseDeleteNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库删除';
    }
    if (!this.metadata.description) {
      this.metadata.description = '执行DELETE操作，支持条件删除';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'table',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '表名',
      defaultValue: ''
    });

    this.addInput({
      name: 'where',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '删除条件'
    });

    this.addInput({
      name: 'limit',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '限制删除行数',
      defaultValue: 0
    });

    this.addInput({
      name: 'confirmDelete',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '确认删除（安全检查）',
      defaultValue: false
    });

    // 输出
    this.addOutput({
      name: 'affectedRows',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '删除行数'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const connection = inputs.connection;
      const table = inputs.table;
      const where = inputs.where;
      const limit = inputs.limit || 0;
      const confirmDelete = inputs.confirmDelete;

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      if (!table) {
        throw new Error('表名不能为空');
      }

      if (!confirmDelete) {
        throw new Error('必须确认删除操作');
      }

      // 构建删除语句
      let sql = `DELETE FROM ${table}`;
      const params: any[] = [];

      // 添加WHERE条件
      if (where && Object.keys(where).length > 0) {
        const whereFields = Object.keys(where).map(field => `${field} = ?`).join(' AND ');
        sql += ` WHERE ${whereFields}`;
        params.push(...Object.values(where));
      } else {
        throw new Error('删除操作必须包含WHERE条件');
      }

      // 添加LIMIT
      if (limit > 0) {
        sql += ` LIMIT ${limit}`;
      }

      // 执行删除
      const result = await connection.execute(sql, params);

      this.setOutputValue('affectedRows', result.affectedRows);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 数据库事务节点
 */
export class DatabaseTransactionNode extends FlowNode {
  private transaction: any = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['started', 'committed', 'rolledBack', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库事务';
    }
    if (!this.metadata.description) {
      this.metadata.description = '数据库事务管理（开始、提交、回滚）';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'action',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '事务操作（begin/commit/rollback）',
      defaultValue: 'begin'
    });

    this.addInput({
      name: 'isolationLevel',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '隔离级别',
      defaultValue: 'READ_COMMITTED'
    });

    // 输出
    this.addOutput({
      name: 'transaction',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '事务对象'
    });

    this.addOutput({
      name: 'transactionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '事务ID'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const connection = inputs.connection;
      const action = inputs.action || 'begin';
      const isolationLevel = inputs.isolationLevel || 'READ_COMMITTED';

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      switch (action.toLowerCase()) {
        case 'begin':
        case 'start':
          this.transaction = await this.beginTransaction(connection, isolationLevel);
          this.setOutputValue('transaction', this.transaction);
          this.setOutputValue('transactionId', this.transaction.id);
          return 'started';

        case 'commit':
          if (!this.transaction) {
            throw new Error('没有活动的事务');
          }
          await this.commitTransaction(this.transaction);
          this.transaction = null;
          return 'committed';

        case 'rollback':
          if (!this.transaction) {
            throw new Error('没有活动的事务');
          }
          await this.rollbackTransaction(this.transaction);
          this.transaction = null;
          return 'rolledBack';

        default:
          throw new Error(`不支持的事务操作: ${action}`);
      }
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async beginTransaction(connection: any, isolationLevel: string): Promise<any> {
    // 模拟开始事务
    return {
      id: `txn_${Date.now()}`,
      connection,
      isolationLevel,
      startedAt: new Date().toISOString(),
      active: true
    };
  }

  private async commitTransaction(transaction: any): Promise<void> {
    // 模拟提交事务
    transaction.active = false;
    transaction.committedAt = new Date().toISOString();
  }

  private async rollbackTransaction(transaction: any): Promise<void> {
    // 模拟回滚事务
    transaction.active = false;
    transaction.rolledBackAt = new Date().toISOString();
  }
}

/**
 * 数据库结构操作节点
 */
export class DatabaseSchemaNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库结构';
    }
    if (!this.metadata.description) {
      this.metadata.description = '数据库结构操作（创建表、修改表）';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型（create/alter/drop）',
      defaultValue: 'create'
    });

    this.addInput({
      name: 'tableName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '表名',
      defaultValue: ''
    });

    this.addInput({
      name: 'schema',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '表结构定义'
    });

    // 输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '操作结果'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const connection = inputs.connection;
      const operation = inputs.operation || 'create';
      const tableName = inputs.tableName;
      const schema = inputs.schema;

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      if (!tableName) {
        throw new Error('表名不能为空');
      }

      let sql = '';
      let result: any = {};

      switch (operation.toLowerCase()) {
        case 'create':
          sql = this.buildCreateTableSQL(tableName, schema);
          result = await connection.execute(sql);
          result.operation = 'create';
          break;

        case 'alter':
          sql = this.buildAlterTableSQL(tableName, schema);
          result = await connection.execute(sql);
          result.operation = 'alter';
          break;

        case 'drop':
          sql = `DROP TABLE IF EXISTS ${tableName}`;
          result = await connection.execute(sql);
          result.operation = 'drop';
          break;

        default:
          throw new Error(`不支持的操作类型: ${operation}`);
      }

      result.tableName = tableName;
      result.sql = sql;
      this.setOutputValue('result', result);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private buildCreateTableSQL(tableName: string, schema: any): string {
    if (!schema || !schema.columns) {
      throw new Error('表结构定义不能为空');
    }

    const columns = schema.columns.map((col: any) => {
      let columnDef = `${col.name} ${col.type}`;

      if (col.length) {
        columnDef += `(${col.length})`;
      }

      if (col.notNull) {
        columnDef += ' NOT NULL';
      }

      if (col.primaryKey) {
        columnDef += ' PRIMARY KEY';
      }

      if (col.autoIncrement) {
        columnDef += ' AUTO_INCREMENT';
      }

      if (col.defaultValue !== undefined) {
        columnDef += ` DEFAULT ${col.defaultValue}`;
      }

      return columnDef;
    }).join(', ');

    return `CREATE TABLE ${tableName} (${columns})`;
  }

  private buildAlterTableSQL(tableName: string, schema: any): string {
    if (!schema || !schema.action) {
      throw new Error('ALTER操作需要指定action');
    }

    switch (schema.action) {
      case 'ADD_COLUMN':
        return `ALTER TABLE ${tableName} ADD COLUMN ${schema.column.name} ${schema.column.type}`;

      case 'DROP_COLUMN':
        return `ALTER TABLE ${tableName} DROP COLUMN ${schema.columnName}`;

      case 'MODIFY_COLUMN':
        return `ALTER TABLE ${tableName} MODIFY COLUMN ${schema.column.name} ${schema.column.type}`;

      default:
        throw new Error(`不支持的ALTER操作: ${schema.action}`);
    }
  }
}

/**
 * 数据库索引节点
 */
export class DatabaseIndexNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库索引';
    }
    if (!this.metadata.description) {
      this.metadata.description = '数据库索引管理';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型（create/drop/show）',
      defaultValue: 'create'
    });

    this.addInput({
      name: 'tableName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '表名',
      defaultValue: ''
    });

    this.addInput({
      name: 'indexName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '索引名',
      defaultValue: ''
    });

    this.addInput({
      name: 'columns',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '索引列',
      defaultValue: []
    });

    this.addInput({
      name: 'unique',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否唯一索引',
      defaultValue: false
    });

    // 输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '操作结果'
    });

    this.addOutput({
      name: 'indexes',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '索引列表'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const connection = inputs.connection;
      const operation = inputs.operation || 'create';
      const tableName = inputs.tableName;
      const indexName = inputs.indexName;
      const columns = inputs.columns || [];
      const unique = inputs.unique || false;

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      if (!tableName) {
        throw new Error('表名不能为空');
      }

      let sql = '';
      let result: any = {};

      switch (operation.toLowerCase()) {
        case 'create':
          if (!indexName || columns.length === 0) {
            throw new Error('创建索引需要索引名和列名');
          }

          const indexType = unique ? 'UNIQUE INDEX' : 'INDEX';
          const columnList = columns.join(', ');
          sql = `CREATE ${indexType} ${indexName} ON ${tableName} (${columnList})`;

          result = await connection.execute(sql);
          result.operation = 'create';
          result.indexName = indexName;
          break;

        case 'drop':
          if (!indexName) {
            throw new Error('删除索引需要索引名');
          }

          sql = `DROP INDEX ${indexName} ON ${tableName}`;
          result = await connection.execute(sql);
          result.operation = 'drop';
          result.indexName = indexName;
          break;

        case 'show':
          sql = `SHOW INDEX FROM ${tableName}`;
          const indexes = await connection.query(sql);
          this.setOutputValue('indexes', indexes);
          result.operation = 'show';
          result.count = indexes.length;
          break;

        default:
          throw new Error(`不支持的操作类型: ${operation}`);
      }

      result.tableName = tableName;
      result.sql = sql;
      this.setOutputValue('result', result);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 数据库备份节点
 */
export class DatabaseBackupNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库备份';
    }
    if (!this.metadata.description) {
      this.metadata.description = '数据库备份操作';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'backupPath',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '备份文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'tables',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '要备份的表（空表示全部）',
      defaultValue: []
    });

    this.addInput({
      name: 'includeData',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含数据',
      defaultValue: true
    });

    this.addInput({
      name: 'compression',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否压缩',
      defaultValue: false
    });

    // 输出
    this.addOutput({
      name: 'backupInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '备份信息'
    });

    this.addOutput({
      name: 'fileSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '备份文件大小（字节）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const connection = inputs.connection;
      const backupPath = inputs.backupPath;
      const tables = inputs.tables || [];
      const includeData = inputs.includeData !== false;
      const compression = inputs.compression || false;

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      if (!backupPath) {
        throw new Error('备份路径不能为空');
      }

      // 模拟备份过程
      const backupInfo = {
        path: backupPath,
        database: connection.config.database,
        tables: tables.length > 0 ? tables : ['all'],
        includeData,
        compression,
        startTime: new Date().toISOString(),
        endTime: '',
        duration: 0,
        status: 'completed'
      };

      // 模拟备份时间
      await new Promise(resolve => setTimeout(resolve, 1000));

      backupInfo.endTime = new Date().toISOString();
      backupInfo.duration = 1000; // 模拟1秒

      const fileSize = Math.floor(Math.random() * 1000000) + 100000; // 模拟文件大小

      this.setOutputValue('backupInfo', backupInfo);
      this.setOutputValue('fileSize', fileSize);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 数据库迁移节点
 */
export class DatabaseMigrationNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = '数据库迁移';
    }
    if (!this.metadata.description) {
      this.metadata.description = '数据库迁移管理';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据库连接'
    });

    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型（up/down/status）',
      defaultValue: 'up'
    });

    this.addInput({
      name: 'migrationFile',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '迁移文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'version',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标版本',
      defaultValue: ''
    });

    // 输出
    this.addOutput({
      name: 'migrationResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '迁移结果'
    });

    this.addOutput({
      name: 'currentVersion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '当前版本'
    });

    this.addOutput({
      name: 'appliedMigrations',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '已应用的迁移'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const connection = inputs.connection;
      const operation = inputs.operation || 'up';
      const migrationFile = inputs.migrationFile;
      const version = inputs.version;

      if (!connection || !connection.connected) {
        throw new Error('数据库未连接');
      }

      let result: any = {};

      switch (operation.toLowerCase()) {
        case 'up':
          result = await this.runMigrationUp(connection, migrationFile, version);
          break;

        case 'down':
          result = await this.runMigrationDown(connection, migrationFile, version);
          break;

        case 'status':
          result = await this.getMigrationStatus(connection);
          break;

        default:
          throw new Error(`不支持的迁移操作: ${operation}`);
      }

      this.setOutputValue('migrationResult', result);
      this.setOutputValue('currentVersion', result.currentVersion || '1.0.0');
      this.setOutputValue('appliedMigrations', result.appliedMigrations || []);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async runMigrationUp(connection: any, migrationFile: string, version: string): Promise<any> {
    // 模拟向上迁移
    return {
      operation: 'up',
      migrationFile,
      targetVersion: version,
      currentVersion: version || '1.0.1',
      appliedMigrations: ['001_initial', '002_add_users'],
      executedAt: new Date().toISOString()
    };
  }

  private async runMigrationDown(connection: any, migrationFile: string, version: string): Promise<any> {
    // 模拟向下迁移
    return {
      operation: 'down',
      migrationFile,
      targetVersion: version,
      currentVersion: version || '1.0.0',
      appliedMigrations: ['001_initial'],
      executedAt: new Date().toISOString()
    };
  }

  private async getMigrationStatus(connection: any): Promise<any> {
    // 模拟获取迁移状态
    return {
      operation: 'status',
      currentVersion: '1.0.1',
      appliedMigrations: ['001_initial', '002_add_users'],
      pendingMigrations: ['003_add_products'],
      lastMigrationAt: new Date().toISOString()
    };
  }
}

/**
 * 注册数据库节点
 */
export function registerDatabaseNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'database/connect',
    name: '数据库连接',
    category: '数据库节点',
    description: '建立数据库连接（支持MySQL、PostgreSQL、MongoDB）',
    factory: (options: FlowNodeOptions) => new DatabaseConnectNode(options)
  });

  registry.registerNodeType({
    type: 'database/query',
    name: '数据库查询',
    category: '数据库节点',
    description: '执行SELECT查询，支持复杂条件',
    factory: (options: FlowNodeOptions) => new DatabaseQueryNode(options)
  });

  registry.registerNodeType({
    type: 'database/insert',
    name: '数据库插入',
    category: '数据库节点',
    description: '执行INSERT操作，支持批量插入',
    factory: (options: FlowNodeOptions) => new DatabaseInsertNode(options)
  });

  registry.registerNodeType({
    type: 'database/update',
    name: '数据库更新',
    category: '数据库节点',
    description: '执行UPDATE操作，支持条件更新',
    factory: (options: FlowNodeOptions) => new DatabaseUpdateNode(options)
  });

  registry.registerNodeType({
    type: 'database/delete',
    name: '数据库删除',
    category: '数据库节点',
    description: '执行DELETE操作，支持条件删除',
    factory: (options: FlowNodeOptions) => new DatabaseDeleteNode(options)
  });

  registry.registerNodeType({
    type: 'database/transaction',
    name: '数据库事务',
    category: '数据库节点',
    description: '数据库事务管理（开始、提交、回滚）',
    factory: (options: FlowNodeOptions) => new DatabaseTransactionNode(options)
  });

  registry.registerNodeType({
    type: 'database/schema',
    name: '数据库结构',
    category: '数据库节点',
    description: '数据库结构操作（创建表、修改表）',
    factory: (options: FlowNodeOptions) => new DatabaseSchemaNode(options)
  });

  registry.registerNodeType({
    type: 'database/index',
    name: '数据库索引',
    category: '数据库节点',
    description: '数据库索引管理',
    factory: (options: FlowNodeOptions) => new DatabaseIndexNode(options)
  });

  registry.registerNodeType({
    type: 'database/backup',
    name: '数据库备份',
    category: '数据库节点',
    description: '数据库备份操作',
    factory: (options: FlowNodeOptions) => new DatabaseBackupNode(options)
  });

  registry.registerNodeType({
    type: 'database/migration',
    name: '数据库迁移',
    category: '数据库节点',
    description: '数据库迁移管理',
    factory: (options: FlowNodeOptions) => new DatabaseMigrationNode(options)
  });
}
