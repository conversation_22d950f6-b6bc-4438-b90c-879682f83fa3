/**
 * JSON数据处理节点
 * 提供JSON解析、序列化、查询、验证等功能
 */

import { Node, SocketType, SocketDirection, NodeOptions } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * JSON解析节点
 */
export class JSONParseNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'jsonString',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: 'JSON字符串',
      defaultValue: ''
    });

    this.addOutput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '解析后的对象'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否解析成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  protected executeImpl(): any {
    const jsonString = this.getInputValue('jsonString');

    try {
      if (!jsonString || typeof jsonString !== 'string') {
        throw new Error('输入必须是有效的JSON字符串');
      }

      const parsedObject = JSON.parse(jsonString);
      
      this.setOutputValue('object', parsedObject);
      this.setOutputValue('success', true);
      this.setOutputValue('error', '');

      return parsedObject;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      this.setOutputValue('object', null);
      this.setOutputValue('success', false);
      this.setOutputValue('error', errorMessage);

      return null;
    }
  }
}

/**
 * JSON序列化节点
 */
export class JSONStringifyNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '要序列化的对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'indent',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '缩进空格数',
      defaultValue: 0,
      optional: true
    });

    this.addOutput({
      name: 'jsonString',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: 'JSON字符串'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否序列化成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  protected executeImpl(): any {
    const object = this.getInputValue('object');
    const indent = this.getInputValue('indent') || 0;

    try {
      const jsonString = JSON.stringify(object, null, indent);
      
      this.setOutputValue('jsonString', jsonString);
      this.setOutputValue('success', true);
      this.setOutputValue('error', '');

      return jsonString;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '序列化失败';
      
      this.setOutputValue('jsonString', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', errorMessage);

      return '';
    }
  }
}

/**
 * JSON路径查询节点
 */
export class JSONPathNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '要查询的对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'path',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: 'JSON路径 (如: user.name 或 users[0].email)',
      defaultValue: ''
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '查询到的值'
    });

    this.addOutput({
      name: 'found',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否找到值'
    });
  }

  protected executeImpl(): any {
    const object = this.getInputValue('object');
    const path = this.getInputValue('path');

    try {
      if (!object || !path) {
        this.setOutputValue('value', undefined);
        this.setOutputValue('found', false);
        return undefined;
      }

      const value = this.getValueByPath(object, path);
      const found = value !== undefined;

      this.setOutputValue('value', value);
      this.setOutputValue('found', found);

      return value;
    } catch (error) {
      this.setOutputValue('value', undefined);
      this.setOutputValue('found', false);
      return undefined;
    }
  }

  private getValueByPath(obj: any, path: string): any {
    const keys = path.split(/[.\[\]]/).filter(key => key !== '');
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined) {
        return undefined;
      }

      if (Array.isArray(current) && /^\d+$/.test(key)) {
        current = current[parseInt(key, 10)];
      } else if (typeof current === 'object') {
        current = current[key];
      } else {
        return undefined;
      }
    }

    return current;
  }
}

/**
 * JSON合并节点
 */
export class JSONMergeNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'object1',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '第一个对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'object2',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '第二个对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'deep',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否深度合并',
      defaultValue: false,
      optional: true
    });

    this.addOutput({
      name: 'merged',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '合并后的对象'
    });
  }

  protected executeImpl(): any {
    const object1 = this.getInputValue('object1') || {};
    const object2 = this.getInputValue('object2') || {};
    const deep = this.getInputValue('deep') || false;

    let merged: any;

    if (deep) {
      merged = this.deepMerge(object1, object2);
    } else {
      merged = { ...object1, ...object2 };
    }

    this.setOutputValue('merged', merged);
    return merged;
  }

  private deepMerge(obj1: any, obj2: any): any {
    const result = { ...obj1 };

    for (const key in obj2) {
      if (obj2.hasOwnProperty(key)) {
        if (
          typeof obj2[key] === 'object' &&
          obj2[key] !== null &&
          !Array.isArray(obj2[key]) &&
          typeof result[key] === 'object' &&
          result[key] !== null &&
          !Array.isArray(result[key])
        ) {
          result[key] = this.deepMerge(result[key], obj2[key]);
        } else {
          result[key] = obj2[key];
        }
      }
    }

    return result;
  }
}

/**
 * JSON验证节点
 */
export class JSONValidateNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '要验证的对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'schema',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: 'JSON Schema',
      defaultValue: {}
    });

    this.addOutput({
      name: 'valid',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否验证通过'
    });

    this.addOutput({
      name: 'errors',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '验证错误列表'
    });
  }

  protected executeImpl(): any {
    const object = this.getInputValue('object');
    const schema = this.getInputValue('schema');

    try {
      const validation = this.validateObject(object, schema);
      
      this.setOutputValue('valid', validation.valid);
      this.setOutputValue('errors', validation.errors);

      return validation;
    } catch (error) {
      this.setOutputValue('valid', false);
      this.setOutputValue('errors', [error instanceof Error ? error.message : '验证失败']);
      
      return { valid: false, errors: ['验证过程出错'] };
    }
  }

  private validateObject(obj: any, schema: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 简单的JSON Schema验证实现
    if (schema.type) {
      const actualType = Array.isArray(obj) ? 'array' : typeof obj;
      if (actualType !== schema.type) {
        errors.push(`类型不匹配: 期望 ${schema.type}, 实际 ${actualType}`);
      }
    }

    if (schema.required && Array.isArray(schema.required)) {
      for (const requiredField of schema.required) {
        if (!(requiredField in obj)) {
          errors.push(`缺少必需字段: ${requiredField}`);
        }
      }
    }

    if (schema.properties && typeof obj === 'object' && obj !== null) {
      for (const [key, propSchema] of Object.entries(schema.properties)) {
        if (key in obj) {
          const propValidation = this.validateObject(obj[key], propSchema);
          errors.push(...propValidation.errors.map(err => `${key}.${err}`));
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

/**
 * JSON转换节点
 */
export class JSONTransformNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '要转换的对象',
      defaultValue: {}
    });

    this.addInput({
      name: 'mapping',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '字段映射规则',
      defaultValue: {}
    });

    this.addOutput({
      name: 'transformed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '转换后的对象'
    });
  }

  protected executeImpl(): any {
    const object = this.getInputValue('object') || {};
    const mapping = this.getInputValue('mapping') || {};

    const transformed: any = {};

    for (const [targetKey, sourceKey] of Object.entries(mapping)) {
      if (typeof sourceKey === 'string') {
        const value = this.getValueByPath(object, sourceKey);
        if (value !== undefined) {
          transformed[targetKey] = value;
        }
      }
    }

    this.setOutputValue('transformed', transformed);
    return transformed;
  }

  private getValueByPath(obj: any, path: string): any {
    const keys = path.split(/[.\[\]]/).filter(key => key !== '');
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined) {
        return undefined;
      }

      if (Array.isArray(current) && /^\d+$/.test(key)) {
        current = current[parseInt(key, 10)];
      } else if (typeof current === 'object') {
        current = current[key];
      } else {
        return undefined;
      }
    }

    return current;
  }
}

/**
 * 注册JSON节点
 */
export function registerJSONNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'json/parse',
    name: 'JSON解析',
    category: 'JSON节点',
    description: '解析JSON字符串为对象',
    factory: (options: NodeOptions) => new JSONParseNode(options)
  });

  registry.registerNodeType({
    type: 'json/stringify',
    name: 'JSON序列化',
    category: 'JSON节点',
    description: '将对象序列化为JSON字符串',
    factory: (options: NodeOptions) => new JSONStringifyNode(options)
  });

  registry.registerNodeType({
    type: 'json/path',
    name: 'JSON路径查询',
    category: 'JSON节点',
    description: '使用路径查询JSON对象中的值',
    factory: (options: NodeOptions) => new JSONPathNode(options)
  });

  registry.registerNodeType({
    type: 'json/merge',
    name: 'JSON合并',
    category: 'JSON节点',
    description: '合并两个JSON对象',
    factory: (options: NodeOptions) => new JSONMergeNode(options)
  });

  registry.registerNodeType({
    type: 'json/validate',
    name: 'JSON验证',
    category: 'JSON节点',
    description: '使用JSON Schema验证对象',
    factory: (options: NodeOptions) => new JSONValidateNode(options)
  });

  registry.registerNodeType({
    type: 'json/transform',
    name: 'JSON转换',
    category: 'JSON节点',
    description: '根据映射规则转换JSON对象',
    factory: (options: NodeOptions) => new JSONTransformNode(options)
  });
}
