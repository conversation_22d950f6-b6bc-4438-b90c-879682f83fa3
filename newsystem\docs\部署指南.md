# DL引擎部署指南

## 概述

本指南详细介绍了DL引擎多媒体游戏引擎的部署流程，包括开发环境搭建、生产环境部署和云端部署等多种场景。

## 系统要求

### 最低配置要求

**开发环境**:
- CPU: Intel i5 或 AMD Ryzen 5 (4核心)
- 内存: 8GB RAM
- 存储: 50GB 可用空间
- GPU: 支持WebGL 2.0的显卡
- 操作系统: Windows 10/11, macOS 10.15+, Ubuntu 18.04+

**生产环境**:
- CPU: Intel Xeon 或 AMD EPYC (8核心+)
- 内存: 16GB+ RAM
- 存储: 100GB+ SSD
- 网络: 1Gbps+ 带宽
- 操作系统: Ubuntu 20.04 LTS, CentOS 8+

### 推荐配置

**高性能部署**:
- CPU: 16核心+ 处理器
- 内存: 32GB+ RAM
- 存储: 500GB+ NVMe SSD
- GPU: NVIDIA RTX 3080+ (可选，用于AI计算)
- 网络: 10Gbps+ 带宽

## 开发环境搭建

### 1. 环境准备

#### 安装 Node.js
```bash
# 使用 nvm 安装 Node.js 18+
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
```

#### 安装 Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 安装开发工具
```bash
# 全局安装必要工具
npm install -g typescript ts-node .
nodemon
npm install -g @angular/cli @vue/cli create-react-app
```

### 2. 项目克隆与安装

```bash
# 克隆项目
git clone https://github.com/your-org/dl-engine.git
cd dl-engine

# 安装依赖
npm run install:all

# 或者分别安装
cd engine && npm install
cd ../editor && npm install
cd ../server && npm install
```

### 3. 环境配置

#### 创建环境变量文件
```bash
# 复制环境变量模板
cp .env.example .env
cp server/.env.example server/.env
cp editor/.env.example editor/.env
```

#### 配置数据库
```bash
# 启动开发数据库
docker-compose -f docker-compose.dev.yml up -d mongodb redis

# 等待数据库启动
sleep 10

# 初始化数据库
npm run db:migrate
npm run db:seed
```

### 4. 启动开发服务

```bash
# 启动所有服务
npm run dev

# 或者分别启动
npm run dev:engine    # 引擎开发服务
npm run dev:editor    # 编辑器开发服务
npm run dev:server    # 服务器开发服务
```

访问地址：
- 编辑器: http://localhost:3000
- API服务: http://localhost:8080
- 文档: http://localhost:3001

## 生产环境部署

### 1. 使用 Docker 部署

#### 构建镜像
```bash
# 构建所有镜像
docker-compose build

# 或者分别构建
docker build -t dl-engine/editor -f editor/Dockerfile .
docker build -t dl-engine/server -f server/Dockerfile .
docker build -t dl-engine/nginx -f nginx/Dockerfile .
```

#### 启动服务
```bash
# 生产环境启动
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 2. 手动部署

#### 构建项目
```bash
# 构建引擎
cd engine
npm run build

# 构建编辑器
cd ../editor
npm run build

# 构建服务器
cd ../server
npm run build
```

#### 配置 Nginx
```nginx
# /etc/nginx/sites-available/dl-engine
server {
    listen 80;
    server_name your-domain.com;
    
    # 编辑器静态文件
    location / {
        root /var/www/dl-engine/editor/dist;
        try_files $uri $uri/ /index.html;
        
        # 启用 gzip 压缩
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # WebSocket 代理
    location /ws {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 配置 SSL
```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 启动服务
```bash
# 使用 PM2 管理 Node.js 进程
npm install -g pm2

# 启动服务器
cd server
pm2 start ecosystem.config.js --env production

# 设置开机自启
pm2 startup
pm2 save
```

### 3. 数据库配置

#### MongoDB 集群
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  mongodb-primary:
    image: mongo:5.0
    command: mongod --replSet rs0 --bind_ip_all
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb-primary-data:/data/db
    ports:
      - "27017:27017"
  
  mongodb-secondary:
    image: mongo:5.0
    command: mongod --replSet rs0 --bind_ip_all
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb-secondary-data:/data/db
    ports:
      - "27018:27017"
```

#### Redis 集群
```yaml
  redis-master:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-master-data:/data
    ports:
      - "6379:6379"
  
  redis-slave:
    image: redis:7-alpine
    command: redis-server --slaveof redis-master 6379 --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-slave-data:/data
    ports:
      - "6380:6379"
```

## 云端部署

### 1. Kubernetes 部署

#### 创建命名空间
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine
```

#### 部署配置
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dl-engine-server
  namespace: dl-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: dl-engine-server
  template:
    metadata:
      labels:
        app: dl-engine-server
    spec:
      containers:
      - name: server
        image: dl-engine/server:latest
        ports:
        - containerPort: 8080
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: mongodb-uri
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

#### 服务配置
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: dl-engine-server-service
  namespace: dl-engine
spec:
  selector:
    app: dl-engine-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

#### 部署命令
```bash
# 应用配置
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -n dl-engine
kubectl get services -n dl-engine

# 查看日志
kubectl logs -f deployment/dl-engine-server -n dl-engine
```

### 2. AWS 部署

#### 使用 ECS
```json
{
  "family": "dl-engine-server",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "server",
      "image": "your-account.dkr.ecr.region.amazonaws.com/dl-engine/server:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/dl-engine",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### 3. 监控与日志

#### Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'dl-engine-server'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
```

#### Grafana 仪表板
```json
{
  "dashboard": {
    "title": "DL Engine Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      }
    ]
  }
}
```

## 性能优化

### 1. 前端优化
```javascript
// webpack.config.js
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        engine: {
          test: /[\\/]engine[\\/]/,
          name: 'engine',
          chunks: 'all',
        }
      }
    }
  }
};
```

### 2. 后端优化
```javascript
// 启用集群模式
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
} else {
  require('./app.js');
}
```

### 3. 数据库优化
```javascript
// MongoDB 索引
db.scenes.createIndex({ "name": "text", "description": "text" });
db.scenes.createIndex({ "createdAt": -1 });
db.scenes.createIndex({ "author.id": 1, "isPublic": 1 });

// Redis 配置
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 增加 Node.js 内存限制
   node --max-old-space-size=4096 server.js
   ```

2. **端口冲突**
   ```bash
   # 查找占用端口的进程
   lsof -i :8080
   kill -9 <PID>
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose logs mongodb
   
   # 重启数据库
   docker-compose restart mongodb
   ```

### 日志分析
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log | tail -20

# 查看访问日志
tail -f /var/log/nginx/access.log
```

---

*部署指南持续更新，如遇问题请查看故障排除章节或联系技术支持*
