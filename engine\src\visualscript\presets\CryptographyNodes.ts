/**
 * 加密解密节点
 * 提供MD5/SHA256哈希、AES/RSA加密解密、Base64编码解码等功能
 */

import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * MD5哈希节点
 */
export class MD5HashNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'MD5哈希';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'MD5哈希计算';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'input',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入数据',
      defaultValue: ''
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入编码（utf8/hex/base64）',
      defaultValue: 'utf8'
    });

    this.addInput({
      name: 'outputFormat',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输出格式（hex/base64）',
      defaultValue: 'hex'
    });

    // 输出
    this.addOutput({
      name: 'hash',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: 'MD5哈希值'
    });

    this.addOutput({
      name: 'hashInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '哈希信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const input = inputs.input || '';
      const encoding = inputs.encoding || 'utf8';
      const outputFormat = inputs.outputFormat || 'hex';

      if (!input) {
        throw new Error('输入数据不能为空');
      }

      // 使用Web Crypto API计算MD5（注意：Web Crypto API不直接支持MD5，这里使用模拟实现）
      const hash = await this.calculateMD5(input, encoding);
      const formattedHash = this.formatOutput(hash, outputFormat);

      const hashInfo = {
        algorithm: 'MD5',
        inputLength: input.length,
        inputEncoding: encoding,
        outputFormat,
        calculatedAt: new Date().toISOString()
      };

      this.setOutputValue('hash', formattedHash);
      this.setOutputValue('hashInfo', hashInfo);
      
      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async calculateMD5(input: string, encoding: string): Promise<ArrayBuffer> {
    // 简化的MD5实现（实际项目中应使用专门的加密库）
    const encoder = new TextEncoder();
    const data = encoder.encode(input);
    
    // 模拟MD5哈希（实际应使用crypto-js或类似库）
    const hashArray = Array.from(new Uint8Array(await crypto.subtle.digest('SHA-256', data)));
    return new Uint8Array(hashArray.slice(0, 16)).buffer; // 截取前16字节模拟MD5
  }

  private formatOutput(hash: ArrayBuffer, format: string): string {
    const hashArray = Array.from(new Uint8Array(hash));
    
    switch (format.toLowerCase()) {
      case 'hex':
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      
      case 'base64':
        return btoa(String.fromCharCode(...hashArray));
      
      default:
        throw new Error(`不支持的输出格式: ${format}`);
    }
  }
}

/**
 * SHA256哈希节点
 */
export class SHA256HashNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'SHA256哈希';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'SHA256哈希计算';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'input',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入数据',
      defaultValue: ''
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入编码（utf8/hex/base64）',
      defaultValue: 'utf8'
    });

    this.addInput({
      name: 'outputFormat',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输出格式（hex/base64）',
      defaultValue: 'hex'
    });

    // 输出
    this.addOutput({
      name: 'hash',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: 'SHA256哈希值'
    });

    this.addOutput({
      name: 'hashInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '哈希信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const input = inputs.input || '';
      const encoding = inputs.encoding || 'utf8';
      const outputFormat = inputs.outputFormat || 'hex';

      if (!input) {
        throw new Error('输入数据不能为空');
      }

      // 使用Web Crypto API计算SHA256
      const encoder = new TextEncoder();
      const data = encoder.encode(input);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      
      const formattedHash = this.formatOutput(hashBuffer, outputFormat);

      const hashInfo = {
        algorithm: 'SHA-256',
        inputLength: input.length,
        inputEncoding: encoding,
        outputFormat,
        calculatedAt: new Date().toISOString()
      };

      this.setOutputValue('hash', formattedHash);
      this.setOutputValue('hashInfo', hashInfo);
      
      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private formatOutput(hash: ArrayBuffer, format: string): string {
    const hashArray = Array.from(new Uint8Array(hash));
    
    switch (format.toLowerCase()) {
      case 'hex':
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      
      case 'base64':
        return btoa(String.fromCharCode(...hashArray));
      
      default:
        throw new Error(`不支持的输出格式: ${format}`);
    }
  }
}

/**
 * AES加密节点
 */
export class AESEncryptNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'AES加密';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'AES对称加密';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'plaintext',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '明文数据',
      defaultValue: ''
    });

    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '加密密钥',
      defaultValue: ''
    });

    this.addInput({
      name: 'mode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '加密模式（GCM/CBC/CTR）',
      defaultValue: 'GCM'
    });

    this.addInput({
      name: 'keySize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '密钥长度（128/192/256）',
      defaultValue: 256
    });

    // 输出
    this.addOutput({
      name: 'ciphertext',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '密文数据'
    });

    this.addOutput({
      name: 'iv',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '初始化向量'
    });

    this.addOutput({
      name: 'tag',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '认证标签（GCM模式）'
    });

    this.addOutput({
      name: 'encryptionInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '加密信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const plaintext = inputs.plaintext || '';
      const key = inputs.key || '';
      const mode = inputs.mode || 'GCM';
      const keySize = inputs.keySize || 256;

      if (!plaintext) {
        throw new Error('明文数据不能为空');
      }

      if (!key) {
        throw new Error('加密密钥不能为空');
      }

      // 生成或处理密钥
      const cryptoKey = await this.generateKey(key, keySize);
      
      // 生成随机IV
      const iv = crypto.getRandomValues(new Uint8Array(12)); // GCM模式使用12字节IV
      
      // 加密数据
      const encoder = new TextEncoder();
      const data = encoder.encode(plaintext);
      
      const algorithm = {
        name: 'AES-GCM',
        iv: iv
      };

      const encryptedData = await crypto.subtle.encrypt(algorithm, cryptoKey, data);
      
      // 格式化输出
      const ciphertext = btoa(String.fromCharCode(...new Uint8Array(encryptedData)));
      const ivBase64 = btoa(String.fromCharCode(...iv));

      const encryptionInfo = {
        algorithm: `AES-${keySize}-${mode}`,
        keySize,
        mode,
        ivLength: iv.length,
        ciphertextLength: encryptedData.byteLength,
        encryptedAt: new Date().toISOString()
      };

      this.setOutputValue('ciphertext', ciphertext);
      this.setOutputValue('iv', ivBase64);
      this.setOutputValue('tag', ''); // GCM模式的tag包含在ciphertext中
      this.setOutputValue('encryptionInfo', encryptionInfo);
      
      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async generateKey(keyString: string, keySize: number): Promise<CryptoKey> {
    // 从字符串生成密钥
    const encoder = new TextEncoder();
    const keyData = encoder.encode(keyString);

    // 使用PBKDF2派生密钥
    const baseKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      'PBKDF2',
      false,
      ['deriveKey']
    );

    const salt = new Uint8Array(16); // 简化：使用零盐值

    return await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      baseKey,
      {
        name: 'AES-GCM',
        length: keySize
      },
      false,
      ['encrypt', 'decrypt']
    );
  }
}

/**
 * AES解密节点
 */
export class AESDecryptNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'AES解密';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'AES对称解密';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'ciphertext',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '密文数据',
      defaultValue: ''
    });

    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '解密密钥',
      defaultValue: ''
    });

    this.addInput({
      name: 'iv',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '初始化向量',
      defaultValue: ''
    });

    this.addInput({
      name: 'mode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '解密模式（GCM/CBC/CTR）',
      defaultValue: 'GCM'
    });

    this.addInput({
      name: 'keySize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '密钥长度（128/192/256）',
      defaultValue: 256
    });

    // 输出
    this.addOutput({
      name: 'plaintext',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '明文数据'
    });

    this.addOutput({
      name: 'decryptionInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '解密信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const ciphertext = inputs.ciphertext || '';
      const key = inputs.key || '';
      const iv = inputs.iv || '';
      const mode = inputs.mode || 'GCM';
      const keySize = inputs.keySize || 256;

      if (!ciphertext) {
        throw new Error('密文数据不能为空');
      }

      if (!key) {
        throw new Error('解密密钥不能为空');
      }

      if (!iv) {
        throw new Error('初始化向量不能为空');
      }

      // 生成密钥
      const cryptoKey = await this.generateKey(key, keySize);

      // 解码数据
      const ciphertextBytes = Uint8Array.from(atob(ciphertext), c => c.charCodeAt(0));
      const ivBytes = Uint8Array.from(atob(iv), c => c.charCodeAt(0));

      // 解密数据
      const algorithm = {
        name: 'AES-GCM',
        iv: ivBytes
      };

      const decryptedData = await crypto.subtle.decrypt(algorithm, cryptoKey, ciphertextBytes);

      // 转换为字符串
      const decoder = new TextDecoder();
      const plaintext = decoder.decode(decryptedData);

      const decryptionInfo = {
        algorithm: `AES-${keySize}-${mode}`,
        keySize,
        mode,
        plaintextLength: plaintext.length,
        decryptedAt: new Date().toISOString()
      };

      this.setOutputValue('plaintext', plaintext);
      this.setOutputValue('decryptionInfo', decryptionInfo);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async generateKey(keyString: string, keySize: number): Promise<CryptoKey> {
    // 从字符串生成密钥（与加密节点相同的逻辑）
    const encoder = new TextEncoder();
    const keyData = encoder.encode(keyString);

    const baseKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      'PBKDF2',
      false,
      ['deriveKey']
    );

    const salt = new Uint8Array(16);

    return await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      baseKey,
      {
        name: 'AES-GCM',
        length: keySize
      },
      false,
      ['encrypt', 'decrypt']
    );
  }
}

/**
 * RSA加密节点
 */
export class RSAEncryptNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'RSA加密';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'RSA非对称加密';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'plaintext',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '明文数据',
      defaultValue: ''
    });

    this.addInput({
      name: 'publicKey',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '公钥（PEM格式）',
      defaultValue: ''
    });

    this.addInput({
      name: 'keySize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '密钥长度（1024/2048/4096）',
      defaultValue: 2048
    });

    this.addInput({
      name: 'padding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '填充方式（OAEP/PKCS1）',
      defaultValue: 'OAEP'
    });

    // 输出
    this.addOutput({
      name: 'ciphertext',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '密文数据'
    });

    this.addOutput({
      name: 'encryptionInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '加密信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const plaintext = inputs.plaintext || '';
      const publicKeyPem = inputs.publicKey || '';
      const keySize = inputs.keySize || 2048;
      const padding = inputs.padding || 'OAEP';

      if (!plaintext) {
        throw new Error('明文数据不能为空');
      }

      // 如果没有提供公钥，生成一个密钥对
      let publicKey: CryptoKey;
      if (publicKeyPem) {
        publicKey = await this.importPublicKey(publicKeyPem);
      } else {
        const keyPair = await this.generateKeyPair(keySize);
        publicKey = keyPair.publicKey;
      }

      // 加密数据
      const encoder = new TextEncoder();
      const data = encoder.encode(plaintext);

      const algorithm = {
        name: 'RSA-OAEP'
      };

      const encryptedData = await crypto.subtle.encrypt(algorithm, publicKey, data);

      // 格式化输出
      const ciphertext = btoa(String.fromCharCode(...new Uint8Array(encryptedData)));

      const encryptionInfo = {
        algorithm: `RSA-${keySize}-${padding}`,
        keySize,
        padding,
        plaintextLength: plaintext.length,
        ciphertextLength: encryptedData.byteLength,
        encryptedAt: new Date().toISOString()
      };

      this.setOutputValue('ciphertext', ciphertext);
      this.setOutputValue('encryptionInfo', encryptionInfo);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async generateKeyPair(keySize: number): Promise<CryptoKeyPair> {
    return await crypto.subtle.generateKey(
      {
        name: 'RSA-OAEP',
        modulusLength: keySize,
        publicExponent: new Uint8Array([1, 0, 1]),
        hash: 'SHA-256'
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  private async importPublicKey(pemKey: string): Promise<CryptoKey> {
    // 简化的PEM导入（实际项目中需要更完整的PEM解析）
    const binaryDer = atob(pemKey.replace(/-----[^-]+-----/g, '').replace(/\s/g, ''));
    const keyData = Uint8Array.from(binaryDer, c => c.charCodeAt(0));

    return await crypto.subtle.importKey(
      'spki',
      keyData,
      {
        name: 'RSA-OAEP',
        hash: 'SHA-256'
      },
      false,
      ['encrypt']
    );
  }
}

/**
 * RSA解密节点
 */
export class RSADecryptNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'RSA解密';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'RSA非对称解密';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'ciphertext',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '密文数据',
      defaultValue: ''
    });

    this.addInput({
      name: 'privateKey',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '私钥（PEM格式）',
      defaultValue: ''
    });

    this.addInput({
      name: 'keySize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '密钥长度（1024/2048/4096）',
      defaultValue: 2048
    });

    this.addInput({
      name: 'padding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '填充方式（OAEP/PKCS1）',
      defaultValue: 'OAEP'
    });

    // 输出
    this.addOutput({
      name: 'plaintext',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '明文数据'
    });

    this.addOutput({
      name: 'decryptionInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '解密信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const ciphertext = inputs.ciphertext || '';
      const privateKeyPem = inputs.privateKey || '';
      const keySize = inputs.keySize || 2048;
      const padding = inputs.padding || 'OAEP';

      if (!ciphertext) {
        throw new Error('密文数据不能为空');
      }

      // 如果没有提供私钥，生成一个密钥对
      let privateKey: CryptoKey;
      if (privateKeyPem) {
        privateKey = await this.importPrivateKey(privateKeyPem);
      } else {
        const keyPair = await this.generateKeyPair(keySize);
        privateKey = keyPair.privateKey;
      }

      // 解码密文
      const ciphertextBytes = Uint8Array.from(atob(ciphertext), c => c.charCodeAt(0));

      // 解密数据
      const algorithm = {
        name: 'RSA-OAEP'
      };

      const decryptedData = await crypto.subtle.decrypt(algorithm, privateKey, ciphertextBytes);

      // 转换为字符串
      const decoder = new TextDecoder();
      const plaintext = decoder.decode(decryptedData);

      const decryptionInfo = {
        algorithm: `RSA-${keySize}-${padding}`,
        keySize,
        padding,
        plaintextLength: plaintext.length,
        decryptedAt: new Date().toISOString()
      };

      this.setOutputValue('plaintext', plaintext);
      this.setOutputValue('decryptionInfo', decryptionInfo);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }

  private async generateKeyPair(keySize: number): Promise<CryptoKeyPair> {
    return await crypto.subtle.generateKey(
      {
        name: 'RSA-OAEP',
        modulusLength: keySize,
        publicExponent: new Uint8Array([1, 0, 1]),
        hash: 'SHA-256'
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  private async importPrivateKey(pemKey: string): Promise<CryptoKey> {
    // 简化的PEM导入（实际项目中需要更完整的PEM解析）
    const binaryDer = atob(pemKey.replace(/-----[^-]+-----/g, '').replace(/\s/g, ''));
    const keyData = Uint8Array.from(binaryDer, c => c.charCodeAt(0));

    return await crypto.subtle.importKey(
      'pkcs8',
      keyData,
      {
        name: 'RSA-OAEP',
        hash: 'SHA-256'
      },
      false,
      ['decrypt']
    );
  }
}

/**
 * Base64编码节点
 */
export class Base64EncodeNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'Base64编码';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'Base64编码';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'input',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入数据',
      defaultValue: ''
    });

    this.addInput({
      name: 'inputEncoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入编码（utf8/binary）',
      defaultValue: 'utf8'
    });

    this.addInput({
      name: 'urlSafe',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: 'URL安全编码',
      defaultValue: false
    });

    // 输出
    this.addOutput({
      name: 'encoded',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: 'Base64编码结果'
    });

    this.addOutput({
      name: 'encodingInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '编码信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const input = inputs.input || '';
      const inputEncoding = inputs.inputEncoding || 'utf8';
      const urlSafe = inputs.urlSafe || false;

      if (!input) {
        throw new Error('输入数据不能为空');
      }

      let encoded: string;

      if (inputEncoding === 'utf8') {
        // UTF-8字符串编码
        encoded = btoa(unescape(encodeURIComponent(input)));
      } else {
        // 二进制数据编码
        encoded = btoa(input);
      }

      // URL安全编码
      if (urlSafe) {
        encoded = encoded.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
      }

      const encodingInfo = {
        inputLength: input.length,
        outputLength: encoded.length,
        inputEncoding,
        urlSafe,
        encodedAt: new Date().toISOString()
      };

      this.setOutputValue('encoded', encoded);
      this.setOutputValue('encodingInfo', encodingInfo);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * Base64解码节点
 */
export class Base64DecodeNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    if (!this.metadata.name) {
      this.metadata.name = 'Base64解码';
    }
    if (!this.metadata.description) {
      this.metadata.description = 'Base64解码';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入
    this.addInput({
      name: 'encoded',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'Base64编码数据',
      defaultValue: ''
    });

    this.addInput({
      name: 'outputEncoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输出编码（utf8/binary）',
      defaultValue: 'utf8'
    });

    this.addInput({
      name: 'urlSafe',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: 'URL安全解码',
      defaultValue: false
    });

    // 输出
    this.addOutput({
      name: 'decoded',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '解码结果'
    });

    this.addOutput({
      name: 'decodingInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '解码信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  protected async process(inputs: Record<string, any>): Promise<string | null> {
    try {
      const encoded = inputs.encoded || '';
      const outputEncoding = inputs.outputEncoding || 'utf8';
      const urlSafe = inputs.urlSafe || false;

      if (!encoded) {
        throw new Error('编码数据不能为空');
      }

      let processedEncoded = encoded;

      // URL安全解码
      if (urlSafe) {
        processedEncoded = encoded.replace(/-/g, '+').replace(/_/g, '/');
        // 补充填充字符
        while (processedEncoded.length % 4) {
          processedEncoded += '=';
        }
      }

      let decoded: string;

      if (outputEncoding === 'utf8') {
        // 解码为UTF-8字符串
        decoded = decodeURIComponent(escape(atob(processedEncoded)));
      } else {
        // 解码为二进制数据
        decoded = atob(processedEncoded);
      }

      const decodingInfo = {
        inputLength: encoded.length,
        outputLength: decoded.length,
        outputEncoding,
        urlSafe,
        decodedAt: new Date().toISOString()
      };

      this.setOutputValue('decoded', decoded);
      this.setOutputValue('decodingInfo', decodingInfo);

      return 'success';
    } catch (error) {
      this.setOutputValue('error', error.message);
      return 'failed';
    }
  }
}

/**
 * 注册加密解密节点
 */
export function registerCryptographyNodes(registry: NodeRegistry): void {
  registry.registerNodeType({
    type: 'crypto/hash/md5',
    name: 'MD5哈希',
    category: '加密节点',
    description: 'MD5哈希计算',
    factory: (options: FlowNodeOptions) => new MD5HashNode(options)
  });

  registry.registerNodeType({
    type: 'crypto/hash/sha256',
    name: 'SHA256哈希',
    category: '加密节点',
    description: 'SHA256哈希计算',
    factory: (options: FlowNodeOptions) => new SHA256HashNode(options)
  });

  registry.registerNodeType({
    type: 'crypto/aes/encrypt',
    name: 'AES加密',
    category: '加密节点',
    description: 'AES对称加密',
    factory: (options: FlowNodeOptions) => new AESEncryptNode(options)
  });

  registry.registerNodeType({
    type: 'crypto/aes/decrypt',
    name: 'AES解密',
    category: '加密节点',
    description: 'AES对称解密',
    factory: (options: FlowNodeOptions) => new AESDecryptNode(options)
  });

  registry.registerNodeType({
    type: 'crypto/rsa/encrypt',
    name: 'RSA加密',
    category: '加密节点',
    description: 'RSA非对称加密',
    factory: (options: FlowNodeOptions) => new RSAEncryptNode(options)
  });

  registry.registerNodeType({
    type: 'crypto/rsa/decrypt',
    name: 'RSA解密',
    category: '加密节点',
    description: 'RSA非对称解密',
    factory: (options: FlowNodeOptions) => new RSADecryptNode(options)
  });

  registry.registerNodeType({
    type: 'crypto/base64/encode',
    name: 'Base64编码',
    category: '加密节点',
    description: 'Base64编码',
    factory: (options: FlowNodeOptions) => new Base64EncodeNode(options)
  });

  registry.registerNodeType({
    type: 'crypto/base64/decode',
    name: 'Base64解码',
    category: '加密节点',
    description: 'Base64解码',
    factory: (options: FlowNodeOptions) => new Base64DecodeNode(options)
  });
}
