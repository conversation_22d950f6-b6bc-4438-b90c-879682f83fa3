/**
 * 可视化脚本编辑器组件 - 重写版本
 * 提供与底层引擎完全集成的节点式可视化脚本编辑功能
 */
import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import {
  Button,
  Space,
  Tooltip,
  message,
  Drawer,
  Typography,
  Card,
  Divider,
  Spin,
  Alert,
  Modal,
  Input,
  Select,
  Tabs,
  Tree,
  Badge
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  SaveOutlined,
  LoadingOutlined,
  PlusOutlined,
  SettingOutlined,
  FullscreenOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ExpandOutlined,
  BugOutlined,
  BorderOutlined,
  NodeIndexOutlined,
  LinkOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  CodeOutlined,
  ThunderboltOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import NodeSearch from '../visualscript/NodeSearch';
import { EngineService } from '../../services/EngineService';
import {
  VisualScriptToolbar,
  PerformancePanel,
  DebugLogsPanel
} from './VisualScriptEditorCore';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 节点执行状态枚举
 */
enum NodeExecutionState {
  IDLE = 'idle',
  RUNNING = 'running',
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning'
}

/**
 * 连接类型枚举
 */
enum ConnectionType {
  FLOW = 'flow',
  DATA = 'data'
}

/**
 * 插槽接口
 */
interface Socket {
  id: string;
  name: string;
  type: ConnectionType;
  dataType?: string;
  direction: 'input' | 'output';
  description?: string;
  defaultValue?: any;
  optional?: boolean;
  connected?: boolean;
}

/**
 * 节点接口
 */
interface VisualScriptNode {
  id: string;
  type: string;
  name: string;
  category: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  inputs: Socket[];
  outputs: Socket[];
  properties: Record<string, any>;
  executionState: NodeExecutionState;
  breakpoint?: boolean;
  collapsed?: boolean;
  comment?: string;
}

/**
 * 连接接口
 */
interface Connection {
  id: string;
  fromNodeId: string;
  fromSocketId: string;
  toNodeId: string;
  toSocketId: string;
  type: ConnectionType;
  dataType?: string;
}

/**
 * 可视化脚本数据接口
 */
interface VisualScriptData {
  id: string;
  name: string;
  version: string;
  nodes: VisualScriptNode[];
  connections: Connection[];
  variables: Record<string, any>;
  customEvents: string[];
  metadata: {
    created: string;
    modified: string;
    author: string;
    description: string;
  };
}

/**
 * 节点类型信息接口
 */
interface NodeTypeInfo {
  type: string;
  name: string;
  category: string;
  description: string;
  icon?: string;
  color?: string;
  tags?: string[];
  inputs?: Omit<Socket, 'id' | 'connected'>[];
  outputs?: Omit<Socket, 'id' | 'connected'>[];
  properties?: Record<string, any>;
}

/**
 * 执行上下文接口
 */
interface ExecutionContext {
  variables: Record<string, any>;
  currentNode?: string;
  executionStack: string[];
  breakpoints: Set<string>;
  stepMode: boolean;
}

/**
 * 可视化脚本编辑器属性
 */
interface VisualScriptEditorProps {
  /** 脚本数据 */
  value?: VisualScriptData;
  /** 是否只读 */
  readOnly?: boolean;
  /** 高度 */
  height?: string | number;
  /** 宽度 */
  width?: string | number;
  /** 内容变化回调 */
  onChange?: (value: VisualScriptData) => void;
  /** 执行状态变化回调 */
  onExecutionStateChange?: (state: string, context?: ExecutionContext) => void;
  /** 节点选择回调 */
  onNodeSelect?: (node: VisualScriptNode | null) => void;
  /** 连接变化回调 */
  onConnectionChange?: (connections: Connection[]) => void;
  /** 是否显示调试信息 */
  showDebugInfo?: boolean;
  /** 是否启用实时预览 */
  enableRealTimePreview?: boolean;
}

/**
 * 可视化脚本编辑器组件 - 重写版本
 */
const VisualScriptEditor: React.FC<VisualScriptEditorProps> = ({
  value,
  readOnly = false,
  height = '500px',
  width = '100%',
  onChange,
  onExecutionStateChange,
  onNodeSelect,
  onConnectionChange,
  showDebugInfo = false,
  enableRealTimePreview = false
}) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineService = EngineService.getInstance();

  // 核心状态管理
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionContext, setExecutionContext] = useState<ExecutionContext>({
    variables: {},
    executionStack: [],
    breakpoints: new Set(),
    stepMode: false
  });

  // UI状态管理
  const [showNodeSearch, setShowNodeSearch] = useState(false);
  const [showDebugPanel, setShowDebugPanel] = useState(showDebugInfo);
  const [showPropertyPanel, setShowPropertyPanel] = useState(true);
  const [showNodeLibrary, setShowNodeLibrary] = useState(false);
  const [selectedNode, setSelectedNode] = useState<VisualScriptNode | null>(null);
  const [selectedConnection, setSelectedConnection] = useState<Connection | null>(null);

  // 视图状态管理
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [viewportSize, setViewportSize] = useState({ width: 0, height: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // 编辑器设置
  const [gridEnabled, setGridEnabled] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [showMinimap, setShowMinimap] = useState(true);
  const [autoSave, setAutoSave] = useState(true);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 节点相关状态
  const [availableNodeTypes, setAvailableNodeTypes] = useState<NodeTypeInfo[]>([]);
  const [favoriteNodes, setFavoriteNodes] = useState<string[]>([]);
  const [recentNodes, setRecentNodes] = useState<string[]>([]);
  const [nodeSearchQuery, setNodeSearchQuery] = useState('');

  // 连接相关状态
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState<{
    nodeId: string;
    socketId: string;
    type: ConnectionType;
  } | null>(null);

  // 性能和调试状态
  const [performanceMetrics, setPerformanceMetrics] = useState({
    nodeCount: 0,
    connectionCount: 0,
    executionTime: 0,
    memoryUsage: 0
  });
  const [debugLogs, setDebugLogs] = useState<Array<{
    timestamp: number;
    level: 'info' | 'warn' | 'error';
    message: string;
    nodeId?: string;
  }>>([]);

  // 默认脚本数据
  const defaultScriptData: VisualScriptData = {
    id: `script_${Date.now()}`,
    name: 'New Visual Script',
    version: '1.0.0',
    nodes: [],
    connections: [],
    variables: {},
    customEvents: [],
    metadata: {
      created: new Date().toISOString(),
      modified: new Date().toISOString(),
      author: 'User',
      description: 'A new visual script'
    }
  };

  const scriptData = value || defaultScriptData;

  // 从引擎加载可用节点类型
  useEffect(() => {
    const loadNodeTypes = async () => {
      try {
        const visualScriptEngine = engineService.getVisualScriptEngine();
        if (visualScriptEngine && typeof visualScriptEngine.getAvailableNodes === 'function') {
          const engineNodes = visualScriptEngine.getAvailableNodes();

          // 转换引擎节点格式为编辑器格式
          const nodeTypes: NodeTypeInfo[] = engineNodes.map((node: any) => ({
            type: node.type || node.id,
            name: node.name || node.label || node.type,
            category: node.category || 'Custom',
            description: node.description || '',
            icon: node.icon,
            color: node.color || '#1890ff',
            tags: node.tags || [],
            inputs: node.inputs || [],
            outputs: node.outputs || [],
            properties: node.properties || {}
          }));

          setAvailableNodeTypes(nodeTypes);

          // 记录调试信息
          addDebugLog('info', `已加载 ${nodeTypes.length} 个节点类型`);
        } else {
          // 使用默认节点类型
          setAvailableNodeTypes(getDefaultNodeTypes());
          addDebugLog('warn', '引擎未初始化，使用默认节点类型');
        }
      } catch (error) {
        console.error('加载节点类型失败:', error);
        setAvailableNodeTypes(getDefaultNodeTypes());
        addDebugLog('error', `加载节点类型失败: ${error}`);
      }
    };

    loadNodeTypes();
  }, []);

  // 获取默认节点类型
  const getDefaultNodeTypes = (): NodeTypeInfo[] => [
    // 核心节点
    {
      type: 'core/events/onStart',
      name: '开始事件',
      category: '核心节点',
      description: '脚本开始执行时触发',
      icon: 'play',
      color: '#52c41a',
      tags: ['事件', '生命周期'],
      inputs: [],
      outputs: [
        { name: 'start', type: ConnectionType.FLOW, direction: 'output', description: '开始执行' }
      ]
    },
    {
      type: 'core/events/onUpdate',
      name: '更新事件',
      category: '核心节点',
      description: '每帧更新时触发',
      icon: 'sync',
      color: '#1890ff',
      tags: ['事件', '生命周期'],
      inputs: [
        { name: 'deltaTime', type: ConnectionType.DATA, direction: 'input', dataType: 'number', description: '帧间隔时间' }
      ],
      outputs: [
        { name: 'update', type: ConnectionType.FLOW, direction: 'output', description: '更新执行' },
        { name: 'deltaTime', type: ConnectionType.DATA, direction: 'output', dataType: 'number', description: '帧间隔时间' }
      ]
    },
    {
      type: 'core/flow/sequence',
      name: '序列',
      category: '核心节点',
      description: '按顺序执行多个操作',
      icon: 'ordered-list',
      color: '#722ed1',
      tags: ['流程', '控制'],
      inputs: [
        { name: 'flow', type: ConnectionType.FLOW, direction: 'input', description: '触发执行' }
      ],
      outputs: [
        { name: 'output0', type: ConnectionType.FLOW, direction: 'output', description: '第1个输出' },
        { name: 'output1', type: ConnectionType.FLOW, direction: 'output', description: '第2个输出' },
        { name: 'output2', type: ConnectionType.FLOW, direction: 'output', description: '第3个输出' }
      ]
    },
    {
      type: 'math/basic/add',
      name: '加法',
      category: '数学运算',
      description: '计算两个数的和',
      icon: 'plus',
      color: '#fa8c16',
      tags: ['数学', '运算'],
      inputs: [
        { name: 'a', type: ConnectionType.DATA, direction: 'input', dataType: 'number', description: '第一个数', defaultValue: 0 },
        { name: 'b', type: ConnectionType.DATA, direction: 'input', dataType: 'number', description: '第二个数', defaultValue: 0 }
      ],
      outputs: [
        { name: 'result', type: ConnectionType.DATA, direction: 'output', dataType: 'number', description: '计算结果' }
      ]
    },
    {
      type: 'ui/button/create',
      name: '创建按钮',
      category: 'UI节点',
      description: '创建可点击的按钮组件',
      icon: 'button',
      color: '#eb2f96',
      tags: ['UI', '组件'],
      inputs: [
        { name: 'flow', type: ConnectionType.FLOW, direction: 'input', description: '触发创建' },
        { name: 'text', type: ConnectionType.DATA, direction: 'input', dataType: 'string', description: '按钮文本', defaultValue: 'Button' },
        { name: 'position', type: ConnectionType.DATA, direction: 'input', dataType: 'object', description: '按钮位置' }
      ],
      outputs: [
        { name: 'success', type: ConnectionType.FLOW, direction: 'output', description: '创建成功' },
        { name: 'button', type: ConnectionType.DATA, direction: 'output', dataType: 'object', description: '按钮对象' }
      ]
    },
    // HTTP节点
    {
      type: 'http/request/get',
      name: 'HTTP GET请求',
      category: 'HTTP节点',
      description: '发送HTTP GET请求',
      icon: 'cloud-download',
      color: '#13c2c2',
      tags: ['网络', 'HTTP', 'API'],
      inputs: [
        { name: 'flow', type: ConnectionType.FLOW, direction: 'input', description: '触发请求' },
        { name: 'url', type: ConnectionType.DATA, direction: 'input', dataType: 'string', description: '请求URL', defaultValue: '' },
        { name: 'headers', type: ConnectionType.DATA, direction: 'input', dataType: 'object', description: '请求头', defaultValue: {} }
      ],
      outputs: [
        { name: 'success', type: ConnectionType.FLOW, direction: 'output', description: '请求成功' },
        { name: 'error', type: ConnectionType.FLOW, direction: 'output', description: '请求失败' },
        { name: 'response', type: ConnectionType.DATA, direction: 'output', dataType: 'object', description: '响应数据' }
      ]
    },
    {
      type: 'http/request/post',
      name: 'HTTP POST请求',
      category: 'HTTP节点',
      description: '发送HTTP POST请求',
      icon: 'cloud-upload',
      color: '#13c2c2',
      tags: ['网络', 'HTTP', 'API'],
      inputs: [
        { name: 'flow', type: ConnectionType.FLOW, direction: 'input', description: '触发请求' },
        { name: 'url', type: ConnectionType.DATA, direction: 'input', dataType: 'string', description: '请求URL', defaultValue: '' },
        { name: 'data', type: ConnectionType.DATA, direction: 'input', dataType: 'object', description: '请求数据', defaultValue: {} }
      ],
      outputs: [
        { name: 'success', type: ConnectionType.FLOW, direction: 'output', description: '请求成功' },
        { name: 'error', type: ConnectionType.FLOW, direction: 'output', description: '请求失败' },
        { name: 'response', type: ConnectionType.DATA, direction: 'output', dataType: 'object', description: '响应数据' }
      ]
    },
    // JSON节点
    {
      type: 'json/parse',
      name: 'JSON解析',
      category: 'JSON节点',
      description: '解析JSON字符串为对象',
      icon: 'file-text',
      color: '#f759ab',
      tags: ['数据', 'JSON', '解析'],
      inputs: [
        { name: 'jsonString', type: ConnectionType.DATA, direction: 'input', dataType: 'string', description: 'JSON字符串', defaultValue: '' }
      ],
      outputs: [
        { name: 'object', type: ConnectionType.DATA, direction: 'output', dataType: 'object', description: '解析后的对象' },
        { name: 'success', type: ConnectionType.DATA, direction: 'output', dataType: 'boolean', description: '是否解析成功' }
      ]
    },
    {
      type: 'json/stringify',
      name: 'JSON序列化',
      category: 'JSON节点',
      description: '将对象序列化为JSON字符串',
      icon: 'file-text',
      color: '#f759ab',
      tags: ['数据', 'JSON', '序列化'],
      inputs: [
        { name: 'object', type: ConnectionType.DATA, direction: 'input', dataType: 'object', description: '要序列化的对象', defaultValue: {} }
      ],
      outputs: [
        { name: 'jsonString', type: ConnectionType.DATA, direction: 'output', dataType: 'string', description: 'JSON字符串' },
        { name: 'success', type: ConnectionType.DATA, direction: 'output', dataType: 'boolean', description: '是否序列化成功' }
      ]
    },
    // 时间日期节点
    {
      type: 'datetime/current',
      name: '获取当前时间',
      category: '时间日期节点',
      description: '获取当前系统时间',
      icon: 'clock-circle',
      color: '#52c41a',
      tags: ['时间', '日期', '系统'],
      inputs: [
        { name: 'format', type: ConnectionType.DATA, direction: 'input', dataType: 'string', description: '时间格式', defaultValue: 'timestamp' }
      ],
      outputs: [
        { name: 'time', type: ConnectionType.DATA, direction: 'output', dataType: 'any', description: '当前时间' },
        { name: 'timestamp', type: ConnectionType.DATA, direction: 'output', dataType: 'number', description: '时间戳' }
      ]
    },
    {
      type: 'datetime/format',
      name: '时间格式化',
      category: '时间日期节点',
      description: '格式化时间显示',
      icon: 'calendar',
      color: '#52c41a',
      tags: ['时间', '格式化', '显示'],
      inputs: [
        { name: 'time', type: ConnectionType.DATA, direction: 'input', dataType: 'any', description: '时间', defaultValue: Date.now() },
        { name: 'format', type: ConnectionType.DATA, direction: 'input', dataType: 'string', description: '格式字符串', defaultValue: 'YYYY-MM-DD HH:mm:ss' }
      ],
      outputs: [
        { name: 'formatted', type: ConnectionType.DATA, direction: 'output', dataType: 'string', description: '格式化后的时间' }
      ]
    }
  ];

  // 添加调试日志
  const addDebugLog = useCallback((level: 'info' | 'warn' | 'error', message: string, nodeId?: string) => {
    const log = {
      timestamp: Date.now(),
      level,
      message,
      nodeId
    };
    setDebugLogs(prev => [...prev.slice(-99), log]); // 保留最近100条日志
  }, []);

  // 更新性能指标
  useEffect(() => {
    setPerformanceMetrics({
      nodeCount: scriptData.nodes.length,
      connectionCount: scriptData.connections.length,
      executionTime: 0,
      memoryUsage: 0
    });
  }, [scriptData.nodes.length, scriptData.connections.length]);

  // 自动保存逻辑
  useEffect(() => {
    if (autoSave && hasUnsavedChanges) {
      const timer = setTimeout(() => {
        handleSave();
      }, 5000); // 5秒后自动保存

      return () => clearTimeout(timer);
    }
  }, [hasUnsavedChanges, autoSave]);

  // 实时预览逻辑
  useEffect(() => {
    if (enableRealTimePreview && !isExecuting) {
      // 实现实时预览逻辑
      const timer = setTimeout(() => {
        // 预览当前脚本状态
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [scriptData, enableRealTimePreview, isExecuting]);

  // 处理脚本执行 - 与引擎集成
  const handleExecute = useCallback(async () => {
    if (isExecuting) return;

    setIsExecuting(true);
    const startTime = performance.now();

    // 重置执行状态
    const newExecutionContext: ExecutionContext = {
      variables: { ...scriptData.variables },
      executionStack: [],
      breakpoints: executionContext.breakpoints,
      stepMode: executionContext.stepMode
    };
    setExecutionContext(newExecutionContext);

    if (onExecutionStateChange) {
      onExecutionStateChange('running', newExecutionContext);
    }

    try {
      addDebugLog('info', '开始执行脚本');

      // 验证脚本
      const validation = validateScript(scriptData);
      if (!validation.isValid) {
        throw new Error(`脚本验证失败: ${validation.errors.join(', ')}`);
      }

      // 使用引擎执行脚本
      const visualScriptEngine = engineService.getVisualScriptEngine();
      if (visualScriptEngine && typeof visualScriptEngine.executeScript === 'function') {
        // 转换脚本数据为引擎格式
        const engineScript = convertToEngineFormat(scriptData);

        // 执行脚本
        const result = await visualScriptEngine.executeScript(engineScript, newExecutionContext);

        addDebugLog('info', `脚本执行成功，结果: ${JSON.stringify(result)}`);
        message.success(t('可视化脚本.执行成功'));
      } else {
        // 模拟执行
        await simulateExecution(scriptData, newExecutionContext);
        addDebugLog('info', '模拟执行完成');
        message.success(t('可视化脚本.模拟执行成功'));
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      addDebugLog('error', `脚本执行失败: ${errorMessage}`);
      message.error(t('可视化脚本.执行失败') + ': ' + errorMessage);
    } finally {
      const executionTime = performance.now() - startTime;
      setPerformanceMetrics(prev => ({ ...prev, executionTime }));

      setIsExecuting(false);
      if (onExecutionStateChange) {
        onExecutionStateChange('stopped', newExecutionContext);
      }
    }
  }, [isExecuting, scriptData, executionContext, onExecutionStateChange, t, addDebugLog]);

  // 脚本验证函数
  const validateScript = useCallback((script: VisualScriptData): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // 检查是否有开始节点
    const startNodes = script.nodes.filter(node => node.type === 'core/events/onStart');
    if (startNodes.length === 0) {
      errors.push('脚本必须包含至少一个开始事件节点');
    }

    // 检查连接的有效性
    for (const connection of script.connections) {
      const fromNode = script.nodes.find(n => n.id === connection.fromNodeId);
      const toNode = script.nodes.find(n => n.id === connection.toNodeId);

      if (!fromNode) {
        errors.push(`连接源节点不存在: ${connection.fromNodeId}`);
      }
      if (!toNode) {
        errors.push(`连接目标节点不存在: ${connection.toNodeId}`);
      }
    }

    // 检查循环依赖
    if (hasCircularDependency(script)) {
      errors.push('脚本存在循环依赖');
    }

    return { isValid: errors.length === 0, errors };
  }, []);

  // 检查循环依赖
  const hasCircularDependency = useCallback((script: VisualScriptData): boolean => {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const outgoingConnections = script.connections.filter(c => c.fromNodeId === nodeId);
      for (const connection of outgoingConnections) {
        if (dfs(connection.toNodeId)) return true;
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of script.nodes) {
      if (!visited.has(node.id) && dfs(node.id)) {
        return true;
      }
    }

    return false;
  }, []);

  // 转换为引擎格式
  const convertToEngineFormat = useCallback((script: VisualScriptData): any => {
    return {
      id: script.id,
      name: script.name,
      nodes: script.nodes.map(node => ({
        id: node.id,
        type: node.type,
        position: node.position,
        properties: node.properties,
        inputs: node.inputs,
        outputs: node.outputs
      })),
      connections: script.connections.map(conn => ({
        id: conn.id,
        from: { nodeId: conn.fromNodeId, socketId: conn.fromSocketId },
        to: { nodeId: conn.toNodeId, socketId: conn.toSocketId },
        type: conn.type
      })),
      variables: script.variables
    };
  }, []);

  // 模拟执行
  const simulateExecution = useCallback(async (script: VisualScriptData, context: ExecutionContext): Promise<void> => {
    const startNodes = script.nodes.filter(node => node.type === 'core/events/onStart');

    for (const startNode of startNodes) {
      await simulateNodeExecution(startNode, script, context);
    }
  }, []);

  // 模拟节点执行
  const simulateNodeExecution = useCallback(async (node: VisualScriptNode, script: VisualScriptData, context: ExecutionContext): Promise<void> => {
    // 更新节点执行状态
    const updatedNodes = script.nodes.map(n =>
      n.id === node.id ? { ...n, executionState: NodeExecutionState.RUNNING } : n
    );

    if (onChange) {
      onChange({ ...script, nodes: updatedNodes });
    }

    // 模拟执行时间
    await new Promise(resolve => setTimeout(resolve, 100));

    // 检查断点
    if (context.breakpoints.has(node.id)) {
      addDebugLog('info', `在节点 ${node.name} 处暂停`, node.id);
      return;
    }

    // 执行节点逻辑
    try {
      switch (node.type) {
        case 'core/events/onStart':
          addDebugLog('info', '开始事件触发', node.id);
          break;
        case 'core/events/onUpdate':
          addDebugLog('info', '更新事件触发', node.id);
          break;
        case 'math/basic/add':
          const a = node.properties.a || 0;
          const b = node.properties.b || 0;
          const result = a + b;
          addDebugLog('info', `加法运算: ${a} + ${b} = ${result}`, node.id);
          break;
        default:
          addDebugLog('info', `执行节点: ${node.name}`, node.id);
      }

      // 更新为成功状态
      const successNodes = script.nodes.map(n =>
        n.id === node.id ? { ...n, executionState: NodeExecutionState.SUCCESS } : n
      );

      if (onChange) {
        onChange({ ...script, nodes: successNodes });
      }

      // 执行连接的下游节点
      const outgoingConnections = script.connections.filter(c => c.fromNodeId === node.id && c.type === ConnectionType.FLOW);
      for (const connection of outgoingConnections) {
        const nextNode = script.nodes.find(n => n.id === connection.toNodeId);
        if (nextNode) {
          await simulateNodeExecution(nextNode, script, context);
        }
      }

    } catch (error) {
      addDebugLog('error', `节点执行失败: ${error}`, node.id);

      // 更新为错误状态
      const errorNodes = script.nodes.map(n =>
        n.id === node.id ? { ...n, executionState: NodeExecutionState.ERROR } : n
      );

      if (onChange) {
        onChange({ ...script, nodes: errorNodes });
      }
    }
  }, [onChange, addDebugLog]);

  // 处理脚本停止
  const handleStop = useCallback(() => {
    setIsExecuting(false);

    // 重置所有节点状态
    const resetNodes = scriptData.nodes.map(node => ({
      ...node,
      executionState: NodeExecutionState.IDLE
    }));

    if (onChange) {
      onChange({ ...scriptData, nodes: resetNodes });
    }

    if (onExecutionStateChange) {
      onExecutionStateChange('stopped', executionContext);
    }

    addDebugLog('info', '脚本执行已停止');
    message.info(t('可视化脚本.已停止'));
  }, [scriptData, onChange, onExecutionStateChange, executionContext, addDebugLog, t]);

  // 处理节点添加 - 增强版本
  const handleNodeAdd = useCallback((nodeType: string, position?: { x: number; y: number }) => {
    const nodeTypeInfo = availableNodeTypes.find(nt => nt.type === nodeType);
    if (!nodeTypeInfo) {
      message.error(`未知的节点类型: ${nodeType}`);
      return;
    }

    // 创建新节点
    const newNode: VisualScriptNode = {
      id: `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: nodeType,
      name: nodeTypeInfo.name,
      category: nodeTypeInfo.category,
      position: position || {
        x: 100 + Math.random() * 200,
        y: 100 + Math.random() * 200
      },
      size: { width: 200, height: 120 },
      inputs: nodeTypeInfo.inputs?.map((input, index) => ({
        id: `${nodeType}_input_${index}`,
        ...input,
        connected: false
      })) || [],
      outputs: nodeTypeInfo.outputs?.map((output, index) => ({
        id: `${nodeType}_output_${index}`,
        ...output,
        connected: false
      })) || [],
      properties: { ...nodeTypeInfo.properties },
      executionState: NodeExecutionState.IDLE,
      breakpoint: false,
      collapsed: false
    };

    const newScriptData = {
      ...scriptData,
      nodes: [...scriptData.nodes, newNode],
      metadata: {
        ...scriptData.metadata,
        modified: new Date().toISOString()
      }
    };

    if (onChange) {
      onChange(newScriptData);
    }

    // 更新最近使用的节点
    const updatedRecentNodes = [nodeType, ...recentNodes.filter(n => n !== nodeType)].slice(0, 10);
    setRecentNodes(updatedRecentNodes);

    // 选中新添加的节点
    setSelectedNode(newNode);
    if (onNodeSelect) {
      onNodeSelect(newNode);
    }

    setShowNodeSearch(false);
    setHasUnsavedChanges(true);

    addDebugLog('info', `添加节点: ${nodeTypeInfo.name}`, newNode.id);
    message.success(t('可视化脚本.节点已添加'));
  }, [scriptData, onChange, recentNodes, availableNodeTypes, onNodeSelect, addDebugLog, t]);

  // 处理节点删除
  const handleNodeDelete = useCallback((nodeId: string) => {
    const nodeToDelete = scriptData.nodes.find(n => n.id === nodeId);
    if (!nodeToDelete) return;

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除节点 "${nodeToDelete.name}" 吗？这将同时删除所有相关连接。`,
      onOk: () => {
        // 删除相关连接
        const remainingConnections = scriptData.connections.filter(
          conn => conn.fromNodeId !== nodeId && conn.toNodeId !== nodeId
        );

        // 删除节点
        const remainingNodes = scriptData.nodes.filter(n => n.id !== nodeId);

        const newScriptData = {
          ...scriptData,
          nodes: remainingNodes,
          connections: remainingConnections,
          metadata: {
            ...scriptData.metadata,
            modified: new Date().toISOString()
          }
        };

        if (onChange) {
          onChange(newScriptData);
        }

        if (onConnectionChange) {
          onConnectionChange(remainingConnections);
        }

        // 清除选择
        if (selectedNode?.id === nodeId) {
          setSelectedNode(null);
          if (onNodeSelect) {
            onNodeSelect(null);
          }
        }

        setHasUnsavedChanges(true);
        addDebugLog('info', `删除节点: ${nodeToDelete.name}`, nodeId);
        message.success('节点已删除');
      }
    });
  }, [scriptData, onChange, onConnectionChange, selectedNode, onNodeSelect, addDebugLog]);

  // 处理节点复制
  const handleNodeCopy = useCallback((nodeId: string) => {
    const nodeToCopy = scriptData.nodes.find(n => n.id === nodeId);
    if (!nodeToCopy) return;

    const copiedNode: VisualScriptNode = {
      ...nodeToCopy,
      id: `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      position: {
        x: nodeToCopy.position.x + 50,
        y: nodeToCopy.position.y + 50
      },
      inputs: nodeToCopy.inputs.map(input => ({
        ...input,
        id: `${nodeToCopy.type}_input_${Math.random().toString(36).substr(2, 9)}`,
        connected: false
      })),
      outputs: nodeToCopy.outputs.map(output => ({
        ...output,
        id: `${nodeToCopy.type}_output_${Math.random().toString(36).substr(2, 9)}`,
        connected: false
      })),
      executionState: NodeExecutionState.IDLE
    };

    const newScriptData = {
      ...scriptData,
      nodes: [...scriptData.nodes, copiedNode],
      metadata: {
        ...scriptData.metadata,
        modified: new Date().toISOString()
      }
    };

    if (onChange) {
      onChange(newScriptData);
    }

    setSelectedNode(copiedNode);
    if (onNodeSelect) {
      onNodeSelect(copiedNode);
    }

    setHasUnsavedChanges(true);
    addDebugLog('info', `复制节点: ${nodeToCopy.name}`, copiedNode.id);
    message.success('节点已复制');
  }, [scriptData, onChange, onNodeSelect, addDebugLog]);

  // 处理连接创建
  const handleConnectionStart = useCallback((nodeId: string, socketId: string, type: ConnectionType) => {
    setIsConnecting(true);
    setConnectionStart({ nodeId, socketId, type });
    addDebugLog('info', `开始创建连接从 ${nodeId}:${socketId}`);
  }, [addDebugLog]);

  // 处理连接完成
  const handleConnectionEnd = useCallback((nodeId: string, socketId: string, type: ConnectionType) => {
    if (!isConnecting || !connectionStart) return;

    // 验证连接
    if (connectionStart.nodeId === nodeId) {
      message.warning('不能连接到同一个节点');
      setIsConnecting(false);
      setConnectionStart(null);
      return;
    }

    if (connectionStart.type !== type) {
      message.warning('连接类型不匹配');
      setIsConnecting(false);
      setConnectionStart(null);
      return;
    }

    // 检查是否已存在连接
    const existingConnection = scriptData.connections.find(
      conn =>
        (conn.fromNodeId === connectionStart.nodeId && conn.fromSocketId === connectionStart.socketId &&
         conn.toNodeId === nodeId && conn.toSocketId === socketId) ||
        (conn.fromNodeId === nodeId && conn.fromSocketId === socketId &&
         conn.toNodeId === connectionStart.nodeId && conn.toSocketId === connectionStart.socketId)
    );

    if (existingConnection) {
      message.warning('连接已存在');
      setIsConnecting(false);
      setConnectionStart(null);
      return;
    }

    // 创建新连接
    const newConnection: Connection = {
      id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromNodeId: connectionStart.nodeId,
      fromSocketId: connectionStart.socketId,
      toNodeId: nodeId,
      toSocketId: socketId,
      type: type
    };

    const newScriptData = {
      ...scriptData,
      connections: [...scriptData.connections, newConnection],
      metadata: {
        ...scriptData.metadata,
        modified: new Date().toISOString()
      }
    };

    if (onChange) {
      onChange(newScriptData);
    }

    if (onConnectionChange) {
      onConnectionChange(newScriptData.connections);
    }

    setIsConnecting(false);
    setConnectionStart(null);
    setHasUnsavedChanges(true);

    addDebugLog('info', `创建连接: ${connectionStart.nodeId}:${connectionStart.socketId} -> ${nodeId}:${socketId}`);
    message.success('连接已创建');
  }, [isConnecting, connectionStart, scriptData, onChange, onConnectionChange, addDebugLog]);

  // 处理连接删除
  const handleConnectionDelete = useCallback((connectionId: string) => {
    const connectionToDelete = scriptData.connections.find(c => c.id === connectionId);
    if (!connectionToDelete) return;

    const remainingConnections = scriptData.connections.filter(c => c.id !== connectionId);

    const newScriptData = {
      ...scriptData,
      connections: remainingConnections,
      metadata: {
        ...scriptData.metadata,
        modified: new Date().toISOString()
      }
    };

    if (onChange) {
      onChange(newScriptData);
    }

    if (onConnectionChange) {
      onConnectionChange(remainingConnections);
    }

    setHasUnsavedChanges(true);
    addDebugLog('info', `删除连接: ${connectionToDelete.fromNodeId} -> ${connectionToDelete.toNodeId}`);
    message.success('连接已删除');
  }, [scriptData, onChange, onConnectionChange, addDebugLog]);

  // 处理收藏节点切换
  const handleToggleFavorite = useCallback((nodeType: string) => {
    const isFavorite = favoriteNodes.includes(nodeType);
    const updatedFavorites = isFavorite
      ? favoriteNodes.filter(n => n !== nodeType)
      : [...favoriteNodes, nodeType];

    setFavoriteNodes(updatedFavorites);
    message.success(isFavorite ? t('可视化脚本.已取消收藏') : t('可视化脚本.已收藏'));
  }, [favoriteNodes, t]);

  // 处理断点切换
  const handleToggleBreakpoint = useCallback((nodeId: string) => {
    const updatedNodes = scriptData.nodes.map(node =>
      node.id === nodeId ? { ...node, breakpoint: !node.breakpoint } : node
    );

    const newScriptData = {
      ...scriptData,
      nodes: updatedNodes,
      metadata: {
        ...scriptData.metadata,
        modified: new Date().toISOString()
      }
    };

    if (onChange) {
      onChange(newScriptData);
    }

    const node = updatedNodes.find(n => n.id === nodeId);
    if (node) {
      const newBreakpoints = new Set(executionContext.breakpoints);
      if (node.breakpoint) {
        newBreakpoints.add(nodeId);
      } else {
        newBreakpoints.delete(nodeId);
      }

      setExecutionContext(prev => ({ ...prev, breakpoints: newBreakpoints }));
      addDebugLog('info', `${node.breakpoint ? '设置' : '取消'}断点: ${node.name}`, nodeId);
    }

    setHasUnsavedChanges(true);
  }, [scriptData, onChange, executionContext, addDebugLog]);

  // 处理保存
  const handleSave = useCallback(() => {
    if (onChange) {
      const savedScriptData = {
        ...scriptData,
        metadata: {
          ...scriptData.metadata,
          modified: new Date().toISOString()
        }
      };

      onChange(savedScriptData);
      setLastSaved(new Date());
      setHasUnsavedChanges(false);

      addDebugLog('info', '脚本已保存');
      message.success(t('可视化脚本.保存成功'));
    }
  }, [scriptData, onChange, addDebugLog, t]);

  // 处理撤销
  const handleUndo = useCallback(() => {
    // TODO: 实现撤销逻辑 - 需要历史记录系统
    addDebugLog('info', '撤销操作');
    message.info(t('可视化脚本.撤销'));
  }, [addDebugLog, t]);

  // 处理重做
  const handleRedo = useCallback(() => {
    // TODO: 实现重做逻辑 - 需要历史记录系统
    addDebugLog('info', '重做操作');
    message.info(t('可视化脚本.重做'));
  }, [addDebugLog, t]);

  // 处理放大
  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => {
      const newZoom = Math.min(prev * 1.2, 3);
      addDebugLog('info', `缩放级别: ${Math.round(newZoom * 100)}%`);
      return newZoom;
    });
  }, [addDebugLog]);

  // 处理缩小
  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => {
      const newZoom = Math.max(prev / 1.2, 0.1);
      addDebugLog('info', `缩放级别: ${Math.round(newZoom * 100)}%`);
      return newZoom;
    });
  }, [addDebugLog]);

  // 处理适应屏幕
  const handleFitToScreen = useCallback(() => {
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
    addDebugLog('info', '重置视图到适应屏幕');
  }, [addDebugLog]);

  // 清空调试日志
  const handleClearDebugLogs = useCallback(() => {
    setDebugLogs([]);
    message.success('调试日志已清空');
  }, []);

  // 处理节点属性更新
  const handleNodePropertyUpdate = useCallback((nodeId: string, property: string, value: any) => {
    const updatedNodes = scriptData.nodes.map(node =>
      node.id === nodeId
        ? { ...node, properties: { ...node.properties, [property]: value } }
        : node
    );

    const newScriptData = {
      ...scriptData,
      nodes: updatedNodes,
      metadata: {
        ...scriptData.metadata,
        modified: new Date().toISOString()
      }
    };

    if (onChange) {
      onChange(newScriptData);
    }

    setHasUnsavedChanges(true);
    addDebugLog('info', `更新节点属性: ${property} = ${value}`, nodeId);
  }, [scriptData, onChange, addDebugLog]);

  // 处理变量更新
  const handleVariableUpdate = useCallback((name: string, value: any) => {
    const newScriptData = {
      ...scriptData,
      variables: { ...scriptData.variables, [name]: value },
      metadata: {
        ...scriptData.metadata,
        modified: new Date().toISOString()
      }
    };

    if (onChange) {
      onChange(newScriptData);
    }

    setHasUnsavedChanges(true);
    addDebugLog('info', `更新变量: ${name} = ${value}`);
  }, [scriptData, onChange, addDebugLog]);

  // 渲染节点
  const renderNode = useCallback((node: VisualScriptNode) => {
    const nodeTypeInfo = availableNodeTypes.find(nt => nt.type === node.type);
    const isSelected = selectedNode?.id === node.id;

    return (
      <div
        key={node.id}
        style={{
          position: 'absolute',
          left: node.position.x,
          top: node.position.y,
          width: node.size.width,
          minHeight: node.size.height,
          border: `2px solid ${isSelected ? '#1890ff' : (nodeTypeInfo?.color || '#d9d9d9')}`,
          borderRadius: '8px',
          backgroundColor: '#fff',
          boxShadow: isSelected ? '0 4px 12px rgba(24, 144, 255, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',
          cursor: 'move',
          userSelect: 'none',
          opacity: node.executionState === NodeExecutionState.RUNNING ? 0.8 : 1,
          transform: node.executionState === NodeExecutionState.RUNNING ? 'scale(1.05)' : 'scale(1)',
          transition: 'all 0.2s ease'
        }}
        onClick={() => {
          setSelectedNode(node);
          if (onNodeSelect) {
            onNodeSelect(node);
          }
        }}
      >
        {/* 节点头部 */}
        <div style={{
          padding: '8px 12px',
          backgroundColor: nodeTypeInfo?.color || '#f0f0f0',
          color: '#fff',
          borderRadius: '6px 6px 0 0',
          fontSize: '12px',
          fontWeight: 'bold',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <span>{node.name}</span>
          <Space size={4}>
            {node.breakpoint && (
              <div style={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: '#ff4d4f'
              }} />
            )}
            {node.executionState === NodeExecutionState.RUNNING && (
              <LoadingOutlined style={{ fontSize: 12 }} />
            )}
            {node.executionState === NodeExecutionState.SUCCESS && (
              <div style={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: '#52c41a'
              }} />
            )}
            {node.executionState === NodeExecutionState.ERROR && (
              <div style={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: '#ff4d4f'
              }} />
            )}
          </Space>
        </div>

        {/* 节点内容 */}
        <div style={{ padding: '8px 12px' }}>
          {/* 输入插槽 */}
          {node.inputs.map((input, index) => (
            <div key={input.id} style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: 4,
              fontSize: '11px'
            }}>
              <div
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: input.type === ConnectionType.FLOW ? '#722ed1' : '#1890ff',
                  marginRight: 8,
                  cursor: 'pointer',
                  border: input.connected ? '2px solid #52c41a' : '1px solid #d9d9d9'
                }}
                onClick={() => handleConnectionEnd(node.id, input.id, input.type)}
              />
              <span>{input.name}</span>
            </div>
          ))}

          {/* 输出插槽 */}
          {node.outputs.map((output, index) => (
            <div key={output.id} style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
              marginBottom: 4,
              fontSize: '11px'
            }}>
              <span>{output.name}</span>
              <div
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: output.type === ConnectionType.FLOW ? '#722ed1' : '#1890ff',
                  marginLeft: 8,
                  cursor: 'pointer',
                  border: output.connected ? '2px solid #52c41a' : '1px solid #d9d9d9'
                }}
                onClick={() => handleConnectionStart(node.id, output.id, output.type)}
              />
            </div>
          ))}
        </div>

        {/* 节点操作按钮 */}
        {isSelected && (
          <div style={{
            position: 'absolute',
            top: -30,
            right: 0,
            display: 'flex',
            gap: 4
          }}>
            <Button
              size="small"
              icon={<CopyOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleNodeCopy(node.id);
              }}
            />
            <Button
              size="small"
              icon={<DeleteOutlined />}
              danger
              onClick={(e) => {
                e.stopPropagation();
                handleNodeDelete(node.id);
              }}
            />
            <Button
              size="small"
              icon={<BugOutlined />}
              type={node.breakpoint ? 'primary' : 'default'}
              onClick={(e) => {
                e.stopPropagation();
                handleToggleBreakpoint(node.id);
              }}
            />
          </div>
        )}
      </div>
    );
  }, [
    availableNodeTypes,
    selectedNode,
    onNodeSelect,
    handleConnectionEnd,
    handleConnectionStart,
    handleNodeCopy,
    handleNodeDelete,
    handleToggleBreakpoint
  ]);

  // 渲染连接线
  const renderConnection = useCallback((connection: Connection) => {
    const fromNode = scriptData.nodes.find(n => n.id === connection.fromNodeId);
    const toNode = scriptData.nodes.find(n => n.id === connection.toNodeId);

    if (!fromNode || !toNode) return null;

    const fromSocket = fromNode.outputs.find(o => o.id === connection.fromSocketId);
    const toSocket = toNode.inputs.find(i => i.id === connection.toSocketId);

    if (!fromSocket || !toSocket) return null;

    // 计算连接线的起点和终点
    const fromX = fromNode.position.x + fromNode.size.width;
    const fromY = fromNode.position.y + 40 + (fromNode.outputs.indexOf(fromSocket) * 20);
    const toX = toNode.position.x;
    const toY = toNode.position.y + 40 + (toNode.inputs.indexOf(toSocket) * 20);

    // 贝塞尔曲线控制点
    const controlX1 = fromX + 50;
    const controlY1 = fromY;
    const controlX2 = toX - 50;
    const controlY2 = toY;

    const pathData = `M ${fromX} ${fromY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${toX} ${toY}`;

    const isSelected = selectedConnection?.id === connection.id;
    const color = connection.type === ConnectionType.FLOW ? '#722ed1' : '#1890ff';

    return (
      <g key={connection.id}>
        <path
          d={pathData}
          stroke={color}
          strokeWidth={isSelected ? 3 : 2}
          fill="none"
          style={{ cursor: 'pointer' }}
          onClick={() => setSelectedConnection(connection)}
        />
        {/* 连接线中点的删除按钮 */}
        {isSelected && (
          <circle
            cx={(fromX + toX) / 2}
            cy={(fromY + toY) / 2}
            r={8}
            fill="#ff4d4f"
            style={{ cursor: 'pointer' }}
            onClick={() => handleConnectionDelete(connection.id)}
          />
        )}
      </g>
    );
  }, [scriptData.nodes, selectedConnection, handleConnectionDelete]);

  // 渲染画布
  const renderCanvas = () => (
    <div style={{
      position: 'relative',
      width: '100%',
      height: 'calc(100% - 49px)',
      backgroundColor: '#f8f9fa',
      backgroundImage: gridEnabled ? `
        radial-gradient(circle, #ddd 1px, transparent 1px)
      ` : 'none',
      backgroundSize: '20px 20px',
      overflow: 'hidden'
    }}>
      {/* SVG 连接线层 */}
      <svg
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1
        }}
      >
        <g style={{ pointerEvents: 'all' }}>
          {scriptData.connections.map(renderConnection)}
        </g>
      </svg>

      {/* 节点层 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          transform: `scale(${zoomLevel}) translate(${panOffset.x}px, ${panOffset.y}px)`,
          transformOrigin: '0 0',
          zIndex: 2
        }}
      >
        {scriptData.nodes.map(renderNode)}
      </div>

      {/* 空状态提示 */}
      {scriptData.nodes.length === 0 && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: '#999',
          zIndex: 3
        }}>
          <Title level={4} type="secondary">
            {t('可视化脚本.空画布标题')}
          </Title>
          <Text type="secondary">
            {t('可视化脚本.空画布描述')}
          </Text>
          <br />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowNodeSearch(true)}
            style={{ marginTop: '16px' }}
            disabled={readOnly}
          >
            {t('可视化脚本.添加第一个节点')}
          </Button>
        </div>
      )}

      {/* 小地图 */}
      {showMinimap && scriptData.nodes.length > 0 && (
        <div style={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          width: 200,
          height: 150,
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
          zIndex: 4
        }}>
          <div style={{
            padding: '4px 8px',
            fontSize: '12px',
            fontWeight: 'bold',
            borderBottom: '1px solid #f0f0f0'
          }}>
            小地图
          </div>
          {/* 小地图内容 - 简化的节点表示 */}
          <div style={{ padding: 8, position: 'relative', height: 'calc(100% - 32px)' }}>
            {scriptData.nodes.map(node => (
              <div
                key={node.id}
                style={{
                  position: 'absolute',
                  left: node.position.x / 10,
                  top: node.position.y / 10,
                  width: 8,
                  height: 6,
                  backgroundColor: selectedNode?.id === node.id ? '#1890ff' : '#d9d9d9',
                  borderRadius: 1
                }}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div style={{
      height,
      width,
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
      overflow: 'hidden',
      backgroundColor: '#fff',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* 工具栏 */}
      <VisualScriptToolbar
        isExecuting={isExecuting}
        readOnly={readOnly}
        zoomLevel={zoomLevel}
        gridEnabled={gridEnabled}
        showDebugPanel={showDebugPanel}
        hasUnsavedChanges={hasUnsavedChanges}
        onExecute={handleExecute}
        onStop={handleStop}
        onAddNode={() => setShowNodeSearch(true)}
        onSave={handleSave}
        onUndo={handleUndo}
        onRedo={handleRedo}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onFitToScreen={handleFitToScreen}
        onToggleDebugPanel={() => setShowDebugPanel(!showDebugPanel)}
        onToggleGrid={() => setGridEnabled(!gridEnabled)}
        onToggleSettings={() => setShowPropertyPanel(!showPropertyPanel)}
        onToggleFullscreen={() => {}}
      />

      {/* 主要内容区域 */}
      <div style={{
        flex: 1,
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 画布区域 */}
        <div style={{
          flex: 1,
          position: 'relative'
        }}>
          {renderCanvas()}
        </div>

        {/* 右侧面板 */}
        {(showDebugPanel || showPropertyPanel) && (
          <div style={{
            width: 300,
            borderLeft: '1px solid #f0f0f0',
            backgroundColor: '#fafafa',
            overflow: 'auto'
          }}>
            <Tabs defaultActiveKey="debug" size="small">
              {showDebugPanel && (
                <TabPane tab="调试" key="debug">
                  <div style={{ padding: 16 }}>
                    <PerformancePanel
                      metrics={performanceMetrics}
                      isExecuting={isExecuting}
                    />

                    <DebugLogsPanel
                      logs={debugLogs}
                      onClear={handleClearDebugLogs}
                    />

                    {/* 执行上下文信息 */}
                    <Card title="执行上下文" size="small">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <div>
                          <Text type="secondary">变量数量: </Text>
                          <Badge count={Object.keys(scriptData.variables).length} />
                        </div>
                        <div>
                          <Text type="secondary">断点数量: </Text>
                          <Badge count={executionContext.breakpoints.size} />
                        </div>
                        <div>
                          <Text type="secondary">步进模式: </Text>
                          <Tag color={executionContext.stepMode ? 'green' : 'default'}>
                            {executionContext.stepMode ? '开启' : '关闭'}
                          </Tag>
                        </div>
                      </Space>
                    </Card>
                  </div>
                </TabPane>
              )}

              {showPropertyPanel && (
                <TabPane tab="属性" key="properties">
                  <div style={{ padding: 16 }}>
                    {selectedNode ? (
                      <Card title={`节点: ${selectedNode.name}`} size="small">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div>
                            <Text strong>类型: </Text>
                            <Text code>{selectedNode.type}</Text>
                          </div>
                          <div>
                            <Text strong>位置: </Text>
                            <Text>({selectedNode.position.x}, {selectedNode.position.y})</Text>
                          </div>
                          <div>
                            <Text strong>状态: </Text>
                            <Tag color={
                              selectedNode.executionState === NodeExecutionState.SUCCESS ? 'green' :
                              selectedNode.executionState === NodeExecutionState.ERROR ? 'red' :
                              selectedNode.executionState === NodeExecutionState.RUNNING ? 'blue' : 'default'
                            }>
                              {selectedNode.executionState}
                            </Tag>
                          </div>

                          {/* 节点属性编辑 */}
                          <Divider />
                          <Text strong>属性:</Text>
                          {Object.entries(selectedNode.properties).map(([key, value]) => (
                            <div key={key} style={{ marginBottom: 8 }}>
                              <Text>{key}: </Text>
                              <Input
                                size="small"
                                value={String(value)}
                                onChange={(e) => handleNodePropertyUpdate(selectedNode.id, key, e.target.value)}
                                disabled={readOnly}
                              />
                            </div>
                          ))}
                        </Space>
                      </Card>
                    ) : (
                      <Card title="脚本信息" size="small">
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div>
                            <Text strong>名称: </Text>
                            <Text>{scriptData.name}</Text>
                          </div>
                          <div>
                            <Text strong>版本: </Text>
                            <Text>{scriptData.version}</Text>
                          </div>
                          <div>
                            <Text strong>节点数: </Text>
                            <Badge count={scriptData.nodes.length} />
                          </div>
                          <div>
                            <Text strong>连接数: </Text>
                            <Badge count={scriptData.connections.length} />
                          </div>
                          <div>
                            <Text strong>最后修改: </Text>
                            <Text>{new Date(scriptData.metadata.modified).toLocaleString()}</Text>
                          </div>
                        </Space>
                      </Card>
                    )}
                  </div>
                </TabPane>
              )}
            </Tabs>
          </div>
        )}
      </div>

      {/* 节点搜索抽屉 */}
      <Drawer
        title={t('可视化脚本.选择节点')}
        placement="right"
        width={400}
        open={showNodeSearch}
        onClose={() => setShowNodeSearch(false)}
      >
        <NodeSearch
          nodes={availableNodeTypes.map(nodeType => ({
            type: nodeType.type,
            label: nodeType.name,
            description: nodeType.description,
            category: nodeType.category as any,
            icon: nodeType.icon || 'node-index',
            color: nodeType.color || '#1890ff',
            tags: nodeType.tags || []
          }))}
          favoriteNodes={favoriteNodes}
          recentNodes={recentNodes}
          onNodeSelect={handleNodeAdd}
          onToggleFavorite={handleToggleFavorite}
        />
      </Drawer>

      {/* 节点库抽屉 */}
      <Drawer
        title="节点库"
        placement="left"
        width={350}
        open={showNodeLibrary}
        onClose={() => setShowNodeLibrary(false)}
      >
        <div style={{ padding: 16 }}>
          <Input.Search
            placeholder="搜索节点..."
            value={nodeSearchQuery}
            onChange={(e) => setNodeSearchQuery(e.target.value)}
            style={{ marginBottom: 16 }}
          />

          <Tree
            treeData={[
              {
                title: '核心节点',
                key: 'core',
                children: availableNodeTypes
                  .filter(nt => nt.category === '核心节点')
                  .map(nt => ({
                    title: nt.name,
                    key: nt.type,
                    isLeaf: true
                  }))
              },
              {
                title: '数学运算',
                key: 'math',
                children: availableNodeTypes
                  .filter(nt => nt.category === '数学运算')
                  .map(nt => ({
                    title: nt.name,
                    key: nt.type,
                    isLeaf: true
                  }))
              },
              {
                title: 'UI节点',
                key: 'ui',
                children: availableNodeTypes
                  .filter(nt => nt.category === 'UI节点')
                  .map(nt => ({
                    title: nt.name,
                    key: nt.type,
                    isLeaf: true
                  }))
              },
              {
                title: 'HTTP节点',
                key: 'http',
                children: availableNodeTypes
                  .filter(nt => nt.category === 'HTTP节点')
                  .map(nt => ({
                    title: nt.name,
                    key: nt.type,
                    isLeaf: true
                  }))
              },
              {
                title: 'JSON节点',
                key: 'json',
                children: availableNodeTypes
                  .filter(nt => nt.category === 'JSON节点')
                  .map(nt => ({
                    title: nt.name,
                    key: nt.type,
                    isLeaf: true
                  }))
              },
              {
                title: '时间日期节点',
                key: 'datetime',
                children: availableNodeTypes
                  .filter(nt => nt.category === '时间日期节点')
                  .map(nt => ({
                    title: nt.name,
                    key: nt.type,
                    isLeaf: true
                  }))
              },
              {
                title: '文件系统节点',
                key: 'filesystem',
                children: availableNodeTypes
                  .filter(nt => nt.category === '文件系统节点')
                  .map(nt => ({
                    title: nt.name,
                    key: nt.type,
                    isLeaf: true
                  }))
              },
              {
                title: '图像处理节点',
                key: 'image',
                children: availableNodeTypes
                  .filter(nt => nt.category === '图像处理节点')
                  .map(nt => ({
                    title: nt.name,
                    key: nt.type,
                    isLeaf: true
                  }))
              }
            ]}
            onSelect={(keys) => {
              if (keys.length > 0) {
                const nodeType = keys[0] as string;
                if (availableNodeTypes.find(nt => nt.type === nodeType)) {
                  handleNodeAdd(nodeType);
                  setShowNodeLibrary(false);
                }
              }
            }}
          />
        </div>
      </Drawer>
    </div>
  );
};

export default VisualScriptEditor;

// 导出相关类型和枚举
export type {
  VisualScriptData,
  VisualScriptNode,
  Connection,
  Socket,
  NodeTypeInfo,
  ExecutionContext,
  VisualScriptEditorProps
};

export {
  NodeExecutionState,
  ConnectionType
};
